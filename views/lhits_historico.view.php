<?php
#region region DOCS
/** @var Hit[] $hits */
/** @var string $filtro_descripcion */
/** @var string $filtro_requester */
/** @var string $filtro_fecha_desde */
/** @var string $filtro_fecha_hasta */

use App\classes\Hit;

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>My Dash | Hits Histórico</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<?php #endregion head ?>

	<!-- ================== BEGIN page-css ================== -->
	<link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
	<!-- ================== END page-css ================== -->
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<!-- BEGIN breadcrumb -->
		<ol class="breadcrumb float-xl-end">
			<li class="breadcrumb-item"><a href="dashboard">Dashboard</a></li>
			<li class="breadcrumb-item"><a href="listado-hits">Hits</a></li>
			<li class="breadcrumb-item active">Histórico</li>
		</ol>
		<!-- END breadcrumb -->

		<!-- BEGIN page-header -->
		<h1 class="page-header">Hits Histórico <small>Consulta histórica de hits</small></h1>
		<!-- END page-header -->

		<?php #region region SUCCESS ?>
		<?php if (isset($success_display) && $success_display == 'show'): ?>
			<div class="alert alert-success alert-dismissible fade show" role="alert">
				<strong>Éxito!</strong> <?php echo $success_text; ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>
		<?php endif; ?>
		<?php #endregion SUCCESS ?>

		<?php #region region ERROR ?>
		<?php if (isset($error_display) && $error_display == 'show'): ?>
			<div class="alert alert-danger alert-dismissible fade show" role="alert">
				<strong>Error!</strong> <?php echo $error_text; ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>
		<?php endif; ?>
		<?php #endregion ERROR ?>

		<?php #region region FILTERS FORM ?>
		<div class="panel panel-inverse">
			<div class="panel-heading">
				<h4 class="panel-title">Filtros de Búsqueda</h4>
			</div>
			<div class="panel-body">
				<form action="hits-historico" method="POST" id="filtersForm">
					<div class="row mb-3">
						<div class="col-md-3">
							<div class="mb-3">
								<label for="filtro_descripcion" class="form-label">Descripción</label>
								<input type="text" class="form-control" id="filtro_descripcion" name="filtro_descripcion"
									   value="<?php echo htmlspecialchars($filtro_descripcion); ?>"
									   placeholder="Buscar por descripción...">
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="filtro_requester" class="form-label">Requester</label>
								<input type="text" class="form-control" id="filtro_requester" name="filtro_requester"
									   value="<?php echo htmlspecialchars($filtro_requester); ?>"
									   placeholder="Buscar por requester...">
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="filtro_fecha_desde" class="form-label">Fecha Desde</label>
								<div class="input-group">
									<input type="text" id="filtro_fecha_desde" name="filtro_fecha_desde"
										   value="<?php echo htmlspecialchars($filtro_fecha_desde); ?>"
										   class="form-control datepicker" autocomplete="off"/>
									<span class="input-group-text">
										<i class="fa fa-calendar-alt"></i>
									</span>
								</div>
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="filtro_fecha_hasta" class="form-label">Fecha Hasta</label>
								<div class="input-group">
									<input type="text" id="filtro_fecha_hasta" name="filtro_fecha_hasta"
										   value="<?php echo htmlspecialchars($filtro_fecha_hasta); ?>"
										   class="form-control datepicker" autocomplete="off"/>
									<span class="input-group-text">
										<i class="fa fa-calendar-alt"></i>
									</span>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-6">
							<button type="submit" name="sub_filtrar" class="btn btn-primary w-100">
								<i class="fa fa-search me-1"></i> Filtrar
							</button>
						</div>
						<div class="col-md-6">
							<button type="submit" name="sub_limpiar" class="btn btn-secondary w-100">
								<i class="fa fa-times me-1"></i> Limpiar Filtros
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>
		<?php #endregion FILTERS FORM ?>

		<?php #region region RESULTS INFO ?>
		<?php if (!empty($hits)): ?>
			<div class="alert alert-info">
				<i class="fa fa-info-circle me-1"></i>
				Se encontraron <strong><?php echo count($hits); ?></strong> registros que coinciden con los filtros aplicados.
			</div>
		<?php elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_filtrar'])): ?>
			<div class="alert alert-warning">
				<i class="fa fa-exclamation-triangle me-1"></i>
				No se encontraron registros que coincidan con los filtros aplicados.
			</div>
		<?php else: ?>
			<div class="alert alert-info">
				<i class="fa fa-info-circle me-1"></i>
				Utilice los filtros de búsqueda para consultar el histórico de hits.
			</div>
		<?php endif; ?>
		<?php #endregion RESULTS INFO ?>

		<?php #region region PANEL hits ?>
		<?php if (!empty($hits)): ?>
		<div class="panel panel-inverse mt-3">
			<div class="panel-heading">
				<h4 class="panel-title">Resultados del Histórico</h4>
			</div>
			<!-- BEGIN panel-body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE hits ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="text-center">Descripción</th>
						<th class="text-center">Requester</th>
						<th class="text-center">Pago</th>
						<th class="text-center">Fecha</th>
						<th class="text-center">Duración</th>
						<th class="text-center">Estado</th>
						<th class="text-center">Retornado</th>
					</tr>
					</thead>
					<tbody class="fs-13px">
					<?php #region region ARRAY hits ?>
					<?php foreach ($hits as $hit): ?>
						<tr>
							<td>
								<?php echo $hit->getDescripcion(); ?>
								<?php if (!empty($hit->getNota())): ?>
									<i class="fa fa-info-circle ms-1 cursor-pointer" title="<?php echo htmlspecialchars($hit->getNota()); ?>"></i>
								<?php endif; ?>
							</td>
							<td class="text-center">
								<?php echo $hit->getRequester(); ?>
							</td>
							<td class="text-end">
								$<?php echo format_currency_usd($hit->getPago()); ?>
							</td>
							<td class="text-center">
								<?php echo date('Y-m-d H:i', strtotime($hit->getFecha())); ?>
							</td>
							<td class="text-center">
								<?php
								if ($hit->getTerminado() == 1 && !empty($hit->getFechaTerminado())) {
									$start_time = new DateTime($hit->getFecha());
									$end_time = new DateTime($hit->getFechaTerminado());
									$diff = $end_time->diff($start_time);
									$duration_minutes = ($diff->h * 60) + $diff->i;
									echo $duration_minutes . 'm';
								} else {
									echo '-';
								}
								?>
							</td>
							<td class="text-center">
								<?php if ($hit->getTerminado() == 1): ?>
									<span class="badge bg-success">Terminado</span>
								<?php else: ?>
									<span class="badge bg-warning">En Progreso</span>
								<?php endif; ?>
							</td>
							<td class="text-center">
								<?php if ($hit->getRetornado() == 1): ?>
									<span class="badge bg-danger">Sí</span>
								<?php else: ?>
									<span class="badge bg-success">No</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php #endregion ARRAY hits ?>
					</tbody>
				</table>
				<?php #endregion TABLE hits ?>
			</div>
			<!-- END panel-body -->
		</div>
		<?php endif; ?>
		<?php #endregion PANEL hits ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<?php #region region JS date ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/js/datepickerini.js"></script>
<?php #endregion JS date ?>
<?php #endregion JS ?>

</body>
</html>
