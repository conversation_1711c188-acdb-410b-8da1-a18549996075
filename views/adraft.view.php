<!DOCTYPE html>
<html lang="en"  class="dark-mode">
<head>
	<meta charset="utf-8" />
	<title>My Dash | Drafts</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
	<meta content="" name="description" />
	<meta content="" name="author" />
	
	<!-- #head -->
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
</head>
<body>
	<!-- BEGIN #loader -->
	<div id="loader" class="app-loader">
		<span class="spinner"></span>
	</div>
	<!-- END #loader -->

	<!-- BEGIN #app -->
    <div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
		<!-- #header -->
		<?php require_once __ROOT__ . '/views/header.view.php'; ?>

		<!-- #sidebar -->
		<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php require_once __ROOT__ . '/views/labels.view.php'; ?>
			
			<!-- BEGIN page-header -->
			<h1 class="page-header">Drafts</h1>
			<!-- END page-header -->
			
			<form action="adraft" method="POST">	
				<input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">

				<!-- BEGIN row -->
				<div class="row">
					<div class="col">
						<button type="submit" id="sub_save" name="sub_save" class="btn btn-primary w-100">
							Guardar
						</button>
					</div>
                    <?php #region region sub_refresh ?>
                    <div class="col">
                        <button type="submit" id="sub_refresh" name="sub_refresh" class="btn btn-primary w-100">
                            Refrescar
                        </button>
                    </div>
                    <?php #endregion sub_refresh ?>
				</div>
				<!-- END row -->			
				<ul class="nav nav-tabs mt-3">
					<li class="nav-item" onclick="tabselect(1)">
						<a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">Draft 1</a>
					</li>
					<li class="nav-item" onclick="tabselect(2)">
						<a href="#default-tab-2" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 2) ? "active" : ""; ?>">Draft 2</a>
					</li>
					<li class="nav-item" onclick="tabselect(3)">
						<a href="#default-tab-3" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 3) ? "active" : ""; ?>">Shopping</a>
					</li>
				</ul>
				<div class="tab-content panel p-3 rounded-0 rounded-bottom">
					<div class="tab-pane fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">
						<textarea id="draft1" name="draft1" class="form-control no-rounded-pill border-1 border-start-0 border-end-0 border-bottom-0 rounded-0 fs-15px bordered" rows="25"><?php echo $draft->draft1; ?></textarea>
					</div>
					<div class="tab-pane fade <?php echo ($tabselected == 2) ? "active show" : ""; ?>" id="default-tab-2">
						<textarea id="draft2" name="draft2" class="form-control no-rounded-pill border-1 border-start-0 border-end-0 border-bottom-0 rounded-0 fs-15px bordered" rows="25"><?php echo $draft->draft2; ?></textarea>
					</div>
					<div class="tab-pane fade <?php echo ($tabselected == 3) ? "active show" : ""; ?>" id="default-tab-3">
						<textarea id="shopping" name="shopping" class="form-control no-rounded-pill border-1 border-start-0 border-end-0 border-bottom-0 rounded-0 fs-15px bordered" rows="25"><?php echo $draft->shopping; ?></textarea>
					</div>
				</div>
			</form>
		</div>
		<!-- END #content -->
		
		<!-- BEGIN scroll-top-btn -->
		<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
	</div>
	<!-- END #app -->

	<!-- ================== BEGIN core-js ================== -->
	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
	<!-- ================== END core-js ================== -->

	<script type="text/javascript">
		function tabselect(ntab) {
			document.getElementById('tabselected').value = ntab;
		}
	</script>
	

</body>
</html>