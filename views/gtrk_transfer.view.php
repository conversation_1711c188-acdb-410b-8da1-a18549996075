<?php
#region region DOCS
/** @var Config $mod_config */
/** @var float $funds */
/** @var float $commission */
/** @var float $adjusted_funds */
/** @var float $preliminary_send_amount */
/** @var float $paypal_fee */
/** @var float $send_amount */
/** @var bool $calculation_performed */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>My Dash | TRK Transfer</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php require_once __ROOT__ . '/views/labels.view.php'; ?>
		
		<!-- BEGIN page-header -->
		<h4>TRK Transfer Calculator</h4>
		
		<hr>
		<!-- END page-header -->
		
		<?php #region FORM ?>
		<form action="gestion-transfer" method="POST">
			<!-- BEGIN ROW -->
			<div class="row mt-3">
				<!-- BEGIN Funds Input -->
				<div class="col-md-4 col-xs-12">
					<div class="mb-3">
						<label class="form-label">Funds:</label>
						<input type="number" step="0.01" name="funds" id="funds" value="<?php echo @recover_var($funds) ?>" class="form-control" onclick="this.focus();this.select('');" placeholder="Enter amount"/>
					</div>
				</div>
				<!-- END Funds Input -->
				
				<!-- BEGIN TRK Fee Display -->
				<div class="col-md-4 col-xs-12">
					<div class="mb-3">
						<label class="form-label">TRK Fee:</label>
						<input type="text" name="trk_fee_display" id="trk_fee_display" value="<?php echo number_format($mod_config->trk_fee, 2) . '%' ?>" class="form-control" readonly/>
					</div>
				</div>
				<!-- END TRK Fee Display -->
			</div>
			<!-- END ROW -->
			
			<!-- BEGIN ROW -->
			<div class="row">
				<!-- BEGIN Calculate Button -->
				<div class="col-md-8 col-xs-12">
					<button type="submit" id="sub_calcular" name="sub_calcular" class="btn btn-md btn-success w-100 no-border-radious">
						Calcular
					</button>
				</div>
				<!-- END Calculate Button -->
			</div>
			<!-- END ROW -->
			
			<?php if (isset($calculation_performed) && $calculation_performed): ?>
			<!-- BEGIN RESULT ROW -->
			<div class="row mt-4">
				<div class="col-md-8 col-xs-12">
					<!-- BEGIN RESULT PANEL -->
					<div class="panel panel-inverse no-border-radious">
						<div class="panel-heading no-border-radious">
							<h4 class="panel-title">
								<i class="fa fa-calculator me-2"></i>
								Transfer Calculation Result
							</h4>
						</div>
						<!-- BEGIN PANEL body -->
						<div class="p-3">
							<div class="row">
								<div class="col-md-3">
									<div class="card bg-primary text-white mb-3">
										<div class="card-body text-center">
											<h5 class="card-title">
												<i class="fa fa-dollar-sign me-2"></i>
												Total Funds
											</h5>
											<h3 class="card-text"><?php echo format_currency_usd_updated($funds) ?></h3>
										</div>
									</div>
								</div>
								<div class="col-md-3">
									<div class="card bg-warning text-dark mb-3">
										<div class="card-body text-center">
											<h5 class="card-title">
												<i class="fa fa-percentage me-2"></i>
												Commission
											</h5>
											<h3 class="card-text"><?php echo format_currency_usd_updated($commission) ?></h3>
										</div>
									</div>
								</div>
								<div class="col-md-3">
									<div class="card bg-danger text-white mb-3">
										<div class="card-body text-center">
											<h5 class="card-title">
												<i class="fa fa-credit-card me-2"></i>
												PayPal Fee
												<small class="d-block">(max $4.99)</small>
											</h5>
											<h3 class="card-text"><?php echo format_currency_usd_updated($paypal_fee) ?></h3>
										</div>
									</div>
								</div>
								<div class="col-md-3">
									<div class="card bg-success text-white mb-3">
										<div class="card-body text-center">
											<h5 class="card-title">
												<i class="fa fa-paper-plane me-2"></i>
												Send Amount
											</h5>
											<h3 class="card-text"><?php echo format_currency_usd_updated($send_amount) ?></h3>
										</div>
									</div>
								</div>
							</div>
							
							<!-- BEGIN CALCULATION DETAILS -->
							<div class="table-responsive mt-3">
								<table class="table table-hover table-sm">
									<thead>
									<tr>
										<th class="text-center">Description</th>
										<th class="text-center">Value</th>
									</tr>
									</thead>
									<tbody class="fs-12px">
									<tr>
										<td>Original Funds:</td>
										<td class="text-end"><?php echo format_currency_usd_updated($funds); ?></td>
									</tr>
									<tr>
										<td>TRK Fee Percentage:</td>
										<td class="text-end"><?php echo number_format($mod_config->trk_fee, 2) . '%'; ?></td>
									</tr>
									<tr>
										<td>Commission Deduction (<?php echo number_format($mod_config->trk_fee, 2) . '%'; ?> of funds):</td>
										<td class="text-end text-warning">-<?php echo format_currency_usd_updated($commission); ?></td>
									</tr>
									<tr>
										<td>Adjusted Funds (funds - commission):</td>
										<td class="text-end text-info"><?php echo format_currency_usd_updated($adjusted_funds); ?></td>
									</tr>
									<tr>
										<td>Preliminary Calculation:</td>
										<td class="text-end"><code>floor((20/21) * adjusted_funds) = <?php echo format_currency_usd_updated($preliminary_send_amount); ?></code></td>
									</tr>
									<tr>
										<td>PayPal Fee Deduction (max $4.99):</td>
										<td class="text-end text-danger">-<?php echo format_currency_usd_updated($paypal_fee); ?></td>
									</tr>
									<tr class="table-success">
										<td><strong>Final Send Amount:</strong></td>
										<td class="text-end"><strong><?php echo format_currency_usd_updated($send_amount); ?></strong></td>
									</tr>
									<tr class="table-secondary">
										<td><strong>Total Deduction (Commission + PayPal Fee):</strong></td>
										<td class="text-end"><strong>-<?php echo format_currency_usd_updated($commission + $paypal_fee); ?></strong></td>
									</tr>
									</tbody>
								</table>
							</div>
							<!-- END CALCULATION DETAILS -->
						</div>
						<!-- END PANEL body -->
					</div>
					<!-- END RESULT PANEL -->
				</div>
			</div>
			<!-- END RESULT ROW -->
			<?php endif; ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<script src="<?php echo RUTA ?>resources/js/fab.js"></script>
<!-- ================== END core-js ================== -->

<script>
$(document).ready(function() {
	// Enhanced auto-focus: Set focus to funds input when page loads
	setTimeout(function() {
		$('#funds').focus().select();
	}, 100);

	// Auto-select text when clicking on funds input
	$('#funds').on('click', function() {
		$(this).select();
	});

	// Also focus when pressing Enter on the funds field
	$('#funds').on('keypress', function(e) {
		if (e.which === 13) { // Enter key
			$('#sub_calcular').click();
		}
	});
});
</script>
<?php #endregion JS ?>

</body>
</html>
