<?php
#region region DOCS
/** @var Prestamo $modprestamo */
/** @var string $idprestamo */
/** @var PrestamoMovimiento[] $listaMovimientos */

use App\classes\Prestamo;
use App\classes\PrestamoMovimiento;

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Prestamos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h1 class="page-header mb-0">Editar prestamo</h1>
            <a href="lprestamos" class="btn btn-default">
                <i class="fa fa-arrow-left me-1"></i> Regresar
            </a>
        </div>

        <hr>
        <!-- END page-header -->

        <!-- BEGIN tabs navigation -->
        <ul class="nav nav-tabs mb-3" id="prestamoTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                    <i class="fa fa-edit me-1"></i> General
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="movimientos-tab" data-bs-toggle="tab" data-bs-target="#movimientos" type="button" role="tab" aria-controls="movimientos" aria-selected="false">
                    <i class="fa fa-list me-1"></i> Movimientos
                </button>
            </li>
        </ul>
        <!-- END tabs navigation -->

        <!-- BEGIN tab content -->
        <div class="tab-content" id="prestamoTabContent">
            <!-- BEGIN General tab -->
            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                <?php #region region FORM ?>
        <form action="eprestamo" method="POST">
            <input type="hidden" id="idprestamo" name="idprestamo" value="<?php echo @recover_var($idprestamo) ?>">

            <!-- BEGIN row -->
            <div class="row mt-3">
	            <!-- BEGIN Fecha Creación -->
	            <div class="col-md-6 col-xs-12">
		            <label class="form-label">Fecha Creación:</label>
		            <div class="input-group">
			            <input type="text" name="fecha_creacion" id="fecha_creacion" value="<?php echo htmlspecialchars(@recover_var($modprestamo->getFechaCreacion() ? date('Y-m-d', strtotime($modprestamo->getFechaCreacion())) : '')); ?>" class="form-control datepicker" autocomplete="off" onclick="this.focus();this.select('')"/>
			            <span class="input-group-text">
                            <i class="fa fa-calendar-alt"></i>
                        </span>
		            </div>
	            </div>
	            <!-- END Fecha Creación -->
	            <!-- BEGIN text -->
	            <div class="col-md-6 col-xs-12">
                    <label class="form-label">Nombre:</label>
                    <input type="text" name="nombre" id="nombre" value="<?php echo @recover_var($modprestamo->getNombre()) // Use getter ?>" class="form-control" onclick="this.focus();this.select('')" autofocus/>
	            </div>
	            <!-- END text -->
            </div>
            <!-- END row -->

            <!-- BEGIN row - Additional Fields -->
	        <div class="row mt-3">
		        <!-- BEGIN Nota Adicional -->
		        <div class="col-md-12 col-xs-12">
			        <label class="form-label">Nota Adicional:</label>
			        <textarea name="nota_adicional" id="nota_adicional" class="form-control" rows="5" onclick="this.focus();this.select('')"><?php echo htmlspecialchars(@recover_var($modprestamo->getNotaAdicional() ?? '')); ?></textarea>
		        </div>
		        <!-- END Nota Adicional -->
	        </div>
            <!-- END row -->
            <!-- END row - Additional Fields -->

            <!-- BEGIN row - Read-only Info (Improved styling and positioning) -->
            <div class="row mt-3 mb-3">
                <!-- BEGIN Info Panel -->
                <div class="col-md-12">
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Información del Préstamo</h4>
                        </div>
                        <div class="panel-body bg-gray-600">
                            <div class="row">
                                <!-- BEGIN Tipo Display -->
                                <div class="col-md-4 col-xs-12 mb-2 text-center">
                                    <div class="mt-1">
                                        <?php if ($modprestamo->getTipo()): ?>
                                            <span class="badge <?php echo ($modprestamo->getTipo() == 'Debito') ? "bg-danger" : "bg-success"; ?> fs-16px px-3 py-2">
                                                <i class="fa <?php echo ($modprestamo->getTipo() == 'Debito') ? "fa-arrow-down" : "fa-arrow-up"; ?> me-1"></i>
                                                <?php echo htmlspecialchars($modprestamo->getTipo()); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <!-- END Tipo Display -->
                                <!-- BEGIN Valor Inicial Display -->
                                <div class="col-md-4 col-xs-12 mb-2 text-center">
                                    <div class="mt-1">
                                        <span class="badge bg-info fs-16px px-3 py-2">
                                            Inicial:
	                                        <i class="fa fa-dollar-sign me-1"></i>
                                            <?php echo format_currency($modprestamo->getValorInicial() ?? 0); ?>
                                        </span>
                                    </div>
                                </div>
                                <!-- END Valor Inicial Display -->
                                <!-- BEGIN Valor Pendiente Display -->
                                <div class="col-md-4 col-xs-12 mb-2 text-center">
                                    <div class="mt-1">
                                        <span class="badge <?php echo ($modprestamo->getTipo() == 'Debito') ? "bg-danger" : "bg-success"; ?> fs-16px px-3 py-2">
                                            Pendiente:
	                                        <i class="fa fa-dollar-sign me-1"></i>
                                            <?php echo @format_currency($modprestamo->getValor()); ?>
                                        </span>
                                    </div>
                                </div>
                                <!-- END Valor Pendiente Display -->
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Info Panel -->
            </div>
            <!-- END row - Read-only Info -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <?php #region region SUBMIT sub_mod ?>
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_mod" name="sub_mod" class="btn btn-success w-100">
                        <i class="fa fa-save me-1"></i> Modificar
                    </button>
                </div>
                <?php #endregion sub_mod ?>
            </div>
            <!-- END row -->
        </form>
        <?php #endregion form ?>
            </div>
            <!-- END General tab -->

            <!-- BEGIN Movimientos tab -->
            <div class="tab-pane fade" id="movimientos" role="tabpanel" aria-labelledby="movimientos-tab">
                <!-- BEGIN Movimientos Section -->

        <?php #region region FORM Movimiento ?>
        <form action="eprestamo" method="POST" class="mb-4" id="addMovimientoForm">
            <input type="hidden" name="idprestamo" value="<?php echo @recover_var($idprestamo) ?>">
            <input type="hidden" name="action" value="add_movimiento"> <?php // Hidden field to identify the action ?>

            <div class="row">
                <div class="col-md-4 col-xs-12 mb-3">
                    <label class="form-label" for="mov_fecha">Fecha Movimiento:</label>
                    <div class="input-group">
                        <input type="text" name="mov_fecha" id="mov_fecha" class="form-control datepicker" required value="<?php echo date('Y-m-d'); ?>" autocomplete="off">
                        <span class="input-group-text">
                            <i class="fa fa-calendar-alt"></i>
                        </span>
                    </div>
                </div>
                <div class="col-md-4 col-xs-12 mb-3">
                    <label class="form-label" for="mov_valor">Valor Movimiento:</label>
                    <input type="text" name="mov_valor" id="mov_valor" class="form-control" required placeholder="Ej: 50000" inputmode="numeric">
                </div>
                <div class="col-md-4 col-xs-12 mb-3">
                    <label class="form-label" for="mov_nota">Nota (Opcional):</label>
                    <input type="text" name="mov_nota" id="mov_nota" class="form-control" placeholder="Ej: Abono mensual">
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-12">
                    <button type="submit" name="sub_add_movimiento" class="btn btn-primary w-100">
                        Agregar Movimiento
                    </button>
                </div>
            </div>
        </form>
        <?php #endregion form Movimiento ?>

        <?php #region region PANEL Movimientos ?>
        <div class="panel panel-inverse mt-3">
            <div class="panel-heading">
                <h4 class="panel-title d-flex justify-content-between align-items-center">
                    <span>Movimientos del Préstamo:</span>
                    <div>
                        <span class="badge bg-info ms-2 fs-12px">Total: <?php echo $totalMovimientos; ?> movimientos</span>
                        <span class="badge <?php echo ($sumaMovimientos >= 0) ? "bg-success" : "bg-danger"; ?> ms-2 fs-12px">
	                        Pagado: <?php echo formatCurrencyConSigno($sumaMovimientos); ?>
                        </span>
                    </div>
                </h4>
            </div>
            <!-- BEGIN panel-body -->
            <div class="table-nowrap bg-gray-900" style="overflow: auto">
                <?php #region region TABLE Movimientos ?>
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th class="w-50px">Acciones</th>
                            <th class="text-center">Fecha</th>
                            <th class="text-center">Valor</th>
                            <th class="text-center">Nota</th>
                        </tr>
                    </thead>
                    <tbody class="fs-13px">
                        <?php
                        if (!empty($listaMovimientos)): ?>
                            <?php foreach ($listaMovimientos as $movimiento): ?>
                                <tr>
                                    <td class="text-center">
                                        <i class="fa fa-trash fa-md cursor-pointer text-danger"
                                           data-bs-toggle="modal"
                                           data-bs-target="#mdl_del_movimiento"
                                           data-idmovimiento="<?php echo $movimiento->getId(); ?>"
                                           title="Eliminar movimiento"></i>
                                    </td>
                                    <td class="text-center"><?php echo htmlspecialchars($movimiento->getFecha() ?? ''); ?></td>
                                    <?php
                                    // Ensure valor is treated as float for formatting
                                    $valorMovimiento = $movimiento->getValor();
                                    ?>
                                    <td class="text-end <?php echo ($valorMovimiento >= 0) ? "text-success" : "text-danger"; ?>"><?php echo htmlspecialchars(formatCurrencyConSigno($valorMovimiento)); ?></td>
                                    <td><?php echo htmlspecialchars($movimiento->getNota() ?? ''); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center text-muted">No hay movimientos registrados.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
                <?php #endregion table Movimientos ?>
            </div>
            <!-- END panel-body -->
        </div>
        <?php #endregion panel Movimientos ?>
        <!-- END Movimientos Section -->
            </div>
            <!-- END Movimientos tab -->
        </div>
        <!-- END tab content -->

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region MODAL mdl_del_movimiento ?>
<div class="modal fade" id="mdl_del_movimiento">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form action="eprestamo" method="POST" id="deleteMovimientoForm">
                <input type="hidden" name="idprestamo" value="<?php echo @recover_var($idprestamo) ?>">
                <input type="hidden" id="mdl_del_movimiento_idmovimiento" name="idmovimiento">
                <input type="hidden" name="action" value="delete_movimiento">

                <div class="modal-header">
                    <h4 class="modal-title">Eliminar Movimiento</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <p>¿Está seguro que desea eliminar este movimiento?</p>
                    <p class="text-muted small">Esta acción no se puede deshacer.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fa fa-times me-1"></i> Cancelar
                    </button>
                    <button type="submit" name="sub_del_movimiento" class="btn btn-danger">
                        <i class="fa fa-trash me-1"></i> Eliminar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php #endregion mdl_del_movimiento ?>

<?php #region region CSS ?>
<style>
.table-compact-movements {
    background-color: var(--bs-body-bg);
}
.table-compact-movements td,
.table-compact-movements th {
    padding: 0.5rem 0.75rem;
    vertical-align: middle;
}
.table-compact-movements tbody tr {
    background-color: var(--bs-body-bg) !important;
}
.table-compact-movements tbody tr:hover {
    background-color: var(--bs-gray-100) !important;
}

/* Simple Tab Styling */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    color: #6c757d;
    font-weight: 500;
    padding: 10px 16px;
    border: 1px solid transparent;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #0f766e;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 600;
}

.tab-content {
    background: #fff;
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 20px;
    border-radius: 0 0 6px 6px;
}

/* Dark mode adjustments */
.dark-mode .nav-tabs {
    border-bottom-color: #495057;
}

.dark-mode .nav-tabs .nav-link {
    color: #adb5bd;
}

.dark-mode .nav-tabs .nav-link:hover {
    border-color: #495057 #495057 #495057;
    color: #f8f9fa;
}

.dark-mode .nav-tabs .nav-link.active {
    /*color: #5eead4;*/
    background-color: #2d353c;
    border-color: #495057 #495057 #2d3748;
}

.dark-mode .tab-content {
    background: #2d353c;
    border-color: #495057;
    color: #e2e8f0;
}
</style>
<?php #endregion CSS ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<?php #region region JS date ?>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/datepickerini.js"></script>
<?php #endregion datejs ?>

<script type="text/javascript">
    // Function to format number to COP currency string
    function formatCurrencyCOP(value) {
        if (value === null || value === undefined || String(value).trim() === '') return '';
        // Clean the value to a standard number format, removing currency symbols (COP, $) and thousand separators (.) before parsing.
        // Also, handle if the input is already just a number string.
        const number = parseFloat(String(value).replace(/COP|\$|\s/gi, '').replace(/\./g, '').replace(',', '.'));
        if (isNaN(number)) return '';
        // Format as COP currency without decimal places
        return new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(number);
    }

    // Function to clean currency string to a numeric string for server
    function cleanCurrencyForServer(value) {
        if (value === null || value === undefined || String(value).trim() === '') return '';
        // Remove "COP", "$", spaces, and dots (thousand separators for es-CO)
        return String(value).replace(/COP|\$|\s|\./g, '');
    }

    document.addEventListener('DOMContentLoaded', function() {
        const valorMovimientoInput = document.getElementById('mov_valor');
        const addMovimientoForm = document.getElementById('addMovimientoForm');

        // Currency formatting for movement valor
        if (valorMovimientoInput) {
            valorMovimientoInput.addEventListener('input', function(e) {
                const rawValue = e.target.value.replace(/[^0-9]/g, ''); // Keep only digits
                if (rawValue) {
                    e.target.value = formatCurrencyCOP(rawValue);
                } else {
                    e.target.value = '';
                }
            });
        }

        // Clean currency format before submitting movement form
        if (addMovimientoForm && valorMovimientoInput) {
            addMovimientoForm.addEventListener('submit', function() {
                valorMovimientoInput.value = cleanCurrencyForServer(valorMovimientoInput.value);
            });
        }
    });
</script>

<?php #region region JS mdl_del_movimiento ?>
<script type="text/javascript">
    $('#mdl_del_movimiento').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_idmovimiento = button.data('idmovimiento');

        const mdl_del_movimiento_idmovimiento = document.getElementById('mdl_del_movimiento_idmovimiento');
        mdl_del_movimiento_idmovimiento.value = recipient_idmovimiento;
    });
</script>
<?php #endregion js mdl_del_movimiento ?>
<?php #endregion js ?>

</body>
</html>
