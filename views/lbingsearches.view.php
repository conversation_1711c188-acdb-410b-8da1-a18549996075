<?php
#region docs
/** @var string $search */
/** @var BingSearch[] $bingsearches */
/** @var string $bingsearchurl */
/** @var int $opentab */
#endregion docs

?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Bing searches</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <!-- #head -->
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">Bing searches</h1>
        <!-- END page-header -->

        <?php #region region form ?>
        <form action="lbingsearches" method="POST">
            <?php #region region panel addsearch ?>
            <div class="panel panel-inverse ">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Add search:
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="panel-body table-nowrap" style="overflow: auto">
                    <!-- BEGIN row -->
                    <div class="row">
                        <div class="col">
                            <div class="form-floating">
                                <input type="text" class="form-control fs-15px" name="search" id="search" value="<?php echo @recover_var($search) ?>" placeholder="Search:"/>
                                <label for="search" class="d-flex align-items-center fs-15px">
                                    Search:
                                </label>
                            </div>
                        </div>
                    </div>
                    <!-- END row -->
                    <!-- BEGIN row -->
                    <div class="row mt-3">
                        <?php #region region sub_addsearch ?>
                        <div class="col">
                            <button type="submit" id="sub_addsearch" name="sub_addsearch" class="btn btn-primary w-100">
                                Add search
                            </button>
                        </div>
                        <?php #endregion sub_addsearch ?>
                        <?php #region region sub_selrandom ?>
                        <div class="col">
                            <button type="submit" id="sub_selrandom" name="sub_selrandom" class="btn btn-primary w-100">
                                Select randomly
                            </button>
                        </div>
                        <?php #endregion sub_selrandom ?>
                    </div>
                    <!-- END row -->
                </div>
                <!-- END panel-body -->
            </div>
            <?php #endregion panel addsearch ?>
            <?php #region region panel searches ?>
            <div class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Searches:
                        <span class="badge bg-primary rounded-pill fs-11px">
                            <?php echo limpiar_datos(count($bingsearches)); ?>
                        </span>
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="panel-body table-nowrap" style="overflow: auto">
                    <?php #region region table searches ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="w-10px"></th>
                            <th class="w-10px"></th>
                            <th>Search</th>
                        </tr>
                        </thead>
                        <tbody class="fs-14px">
                        <?php #region array searches ?>
                        <?php foreach ($bingsearches as $bingsearch): ?>
                            <tr>
                                <td>
                                    <a class="btn btn-danger btn-xs w-100" href="#mdl_delbingsearch" data-bs-toggle="modal" data-id_bingsearch="<?php echo limpiar_datos($bingsearch->id_bingsearch) ?>">
                                        <i class="fa fa-times fa-lg fa-fw"></i>
                                    </a>
                                </td>
                                <td>
                                    <a class="btn btn-default btn-xs" href="<?php echo $bingsearch->searchcomplete; ?>" target="_blank">
                                        <i class="fa fa-arrow-up-right-from-square fa-lg fa-fw"></i>
                                    </a>
                                </td>
                                <td><?php echo $bingsearch->search; ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <?php #endregion array searches ?>
                        </tbody>
                    </table>
                    <?php #endregion searches ?>
                </div>
                <!-- END panel-body -->
            </div>
            <?php #endregion panel searches ?>
            <?php #region region mdl_delbingsearch ?>
            <div class="modal fade" id="mdl_delbingsearch">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <input type="hidden" id="mdldelbingsearch_id_bingsearch" name="mdldelbingsearch_id_bingsearch">

                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar busqueda</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>¿Esta seguro que desea eliminar esta busqueda?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                            </a>
                            <button type="submit" id="sub_delbingsearch" name="sub_delbingsearch" class="btn btn-danger">
                                <i class="fa fa-check fa-lg fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion mdl_delbingsearch ?>
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region js ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region window onload ?>
<?php if ($opentab == 1): ?>
    <script type="text/javascript">
        window.open('<?php echo $bingsearchurl; ?>', "_blank"); // will open new tab on window.onload
    </script>
<?php endif; ?>
<?php #endregion window onload ?>
<?php #region js mdl_delbingsearch ?>
<script type="text/javascript">
    $('#mdl_delbingsearch').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_id_bingsearch = button.data('id_bingsearch');

        const mdldelbingsearch_id_bingsearch = document.getElementById('mdldelbingsearch_id_bingsearch');

        mdldelbingsearch_id_bingsearch.value = recipient_id_bingsearch;
    })
</script>
<?php #endregion js mdl_delbingsearch ?>
<?php #endregion js ?>

</body>
</html>