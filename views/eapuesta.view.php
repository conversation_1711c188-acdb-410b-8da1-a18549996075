<?php
#region region DOCS
/** @var BtApuesta $mod_btapuesta */
/** @var Source[] $sources */
/** @var Channel[] $channels */
/** @var Equipo[] $equipos */
/** @var Torneo[] $torneos */
/** @var Stakazo[] $stakazos */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Apuestas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>    
    <?php #region region time css ?>
    <link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" />
    <?php #endregion time css ?>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">

        <!-- BEGIN page-header -->
        <h4>Editar apuesta</h4>
        
        <hr>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="eapuesta" method="POST">
            <input type="hidden" id="id_apuesta" name="id_apuesta" value="<?php echo @recover_var($id_apuesta) ?>">

            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN LINK -->
                <div class="col-md-3 col-xs-12">
                    <a href="#mdl_addequipo" data-bs-toggle="modal" class="btn btn-xs btn-primary w-100 no-border-radious">
                        Agregar equipo
                    </a>
                </div>
                <!-- END LINK -->
                <!-- BEGIN LINK -->
                <div class="col-md-3 col-xs-12">
                    <a href="#mdl_addtorneo" data-bs-toggle="modal" class="btn btn-xs btn-primary w-100 no-border-radious">
                        Agregar torneo
                    </a>
                </div>
                <!-- END LINK -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN date -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Fecha:
                        </span>
                        <input type="text" id="fecha" name="fecha" value="<?php echo @recover_var($mod_btapuesta->fecha) ?>" class="form-control form-control-fh fs-12px datepicker no-border-radious" autocomplete="off"/>
                    </div>
                </div>
                <!-- END date -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Source:
                        </span>
                        <input type="text" name="source" id="source" value="<?php echo @recover_var($mod_btapuesta->source->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');" autofocus/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Channel:
                        </span>
                        <input type="text" name="channel" id="channel" value="<?php echo @recover_var($mod_btapuesta->channel->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Stake source:
                        </span>
                        <input type="text" name="stake_source" id="stake_source" value="<?php echo @recover_var($mod_btapuesta->stake_source) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Stake:
                        </span>
                        <input type="text" name="stake" id="stake" value="<?php echo @recover_var($mod_btapuesta->stake) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->                
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Home:
                        </span>
                        <input type="text" name="equipo_home" id="equipo_home" value="<?php echo @recover_var($mod_btapuesta->equipo_home->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Away:
                        </span>
                        <input type="text" name="equipo_away" id="equipo_away" value="<?php echo @recover_var($mod_btapuesta->equipo_away->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Torneos:
                        </span>
                        <input type="text" name="torneo" id="torneo" value="<?php echo @recover_var($mod_btapuesta->torneo->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN date -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Fecha partido:
                        </span>
                        <input type="text" id="fecha_partido" name="fecha_partido" value="<?php echo @recover_var(date('Y-m-d', strtotime($mod_btapuesta->fecha_partido))) ?>" class="form-control form-control-fh fs-12px datepicker no-border-radious" autocomplete="off"/>
                    </div>
                </div>
                <!-- END date -->
                <!-- BEGIN time -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group bootstrap-timepicker no-border-radious">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Hora partido:
                        </span>
                        <input type="text" id="fecha_partido_hora" name="fecha_partido_hora" value="<?php echo @recover_var($mod_btapuesta->fecha_partido_hora) ?>" class="timepicker form-control form-control-fh fs-12px no-border-radious" />
                        <span class="input-group-text input-group-addon bg-gray no-border-radious">
                            <i class="fa fa-clock"></i>
                        </span>
                    </div>
                </div>
                <!-- END time -->                
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Tipo apuesta:
                        </span>
                        <input type="text" name="tipo_apuesta" id="tipo_apuesta" value="<?php echo @recover_var($mod_btapuesta->tipo_apuesta) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Tipo apuesta add.:
                        </span>
                        <input type="text" name="tipo_apuesta_add" id="tipo_apuesta_add" value="<?php echo @recover_var($mod_btapuesta->tipo_apuesta_add) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Tipo apuesta add. 2:
                        </span>
                        <input type="text" name="tipo_apuesta_add2" id="tipo_apuesta_add2" value="<?php echo @recover_var($mod_btapuesta->tipo_apuesta_add2) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Apostado:
                        </span>
                        <input type="text" name="apostado" id="apostado" value="<?php echo @recover_var($mod_btapuesta->apostado) ?>" class="form-control form-control-fh fs-12px no-border-radious" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Cuota source:
                        </span>
                        <input type="text" name="cuota_source" id="cuota_source" value="<?php echo @recover_var($mod_btapuesta->cuota_source) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Cuota grupo:
                        </span>
                        <input type="text" name="cuota_grupo" id="cuota_grupo" value="<?php echo @recover_var($mod_btapuesta->cuota_grupo) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Cuota real:
                        </span>
                        <input type="text" name="cuota_real" id="cuota_real" value="<?php echo @recover_var($mod_btapuesta->cuota_real) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Bankroll:
                        </span>
                        <input type="text" name="bankroll" id="bankroll" value="<?php echo @recover_var($mod_btapuesta->bankroll) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Probabilidades:
                        </span>
                        <input type="text" name="probabilidades" id="probabilidades" value="<?php echo @recover_var($mod_btapuesta->probabilidades) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                            Stakazo:
                        </span>
                        <input type="text" name="stakazo" id="stakazo" value="<?php echo @recover_var($mod_btapuesta->stakazo->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN switch checkbox -->
                <div class="col-md-3 col-xs-12">
                    <div class="form-check form-switch mt-2 mb-2">
                        <input class="form-check-input" type="checkbox" id="es_combinada" name="es_combinada" <?php echo @recoverVarCheckbox($mod_btapuesta->es_combinada); ?>>
                        <label class="form-check-label fs-14px" for="es_combinada">
                            Es combinada
                        </label>
                    </div>
                </div>
                <!-- END switch checkbox -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row">
                <!-- BEGIN switch checkbox -->
                <div class="col-md-3 col-xs-12">
                    <div class="form-check form-switch mt-2 mb-2">
                        <input class="form-check-input" type="checkbox" id="usado" name="usado" <?php echo @recoverVarCheckbox($mod_btapuesta->usado); ?>>
                        <label class="form-check-label fs-14px" for="usado">
                            Usado apuesta personal
                        </label>
                    </div>
                </div>
                <!-- END switch checkbox -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <?php #region region SUBMIT sub_modificar ?>
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_modificar" name="sub_modificar" class="btn btn-lg btn-success w-100 no-border-radious">
                        Modificar
                    </button>
                </div>
                <?php #endregion SUBMIT sub_modificar ?>
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN link -->
                <div class="col-md-12 col-xs-12">
                    <a href="lapuestas" class="btn btn-lg btn-default w-100 no-border-radious">
                        Regresar
                    </a>
                </div>
                <!-- END link -->                
            </div>
            <!-- END ROW -->
                <?php #region region MODAL mdl_addequipo ?>
            <div class="modal fade" id="mdl_addequipo">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Agregar equipo</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <!-- BEGIN text -->
                            <div class="col-md-12 col-xs-12">
                                <div class="input-group">
                                    <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                        Equipo:
                                    </span>
                                    <input type="text" name="addequipo_nombre" id="addequipo_nombre" class="form-control form-control-fh fs-12px no-border-radious text-uppercase"/>
                                </div>
                            </div>
                            <!-- END text -->
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                Cancelar
                            </a>
                            <button type="submit" id="sub_addequipo_agregar" name="sub_addequipo_agregar" class="btn btn-success">
                                Agregar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion MODAL mdl_addequipo ?>
            <?php #region region MODAL mdl_addtorneo ?>
            <div class="modal fade" id="mdl_addtorneo">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Agregar torneo</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <!-- BEGIN text -->
                            <div class="col-md-12 col-xs-12">
                                <div class="input-group">
                                    <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                        Torneo:
                                    </span>
                                    <input type="text" name="addtorneo_nombre" id="addtorneo_nombre" class="form-control form-control-fh fs-12px no-border-radious text-uppercase"/>
                                </div>
                            </div>
                            <!-- END text -->
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                Cancelar
                            </a>
                            <button type="submit" id="sub_addtorneo_agregar" name="sub_addtorneo_agregar" class="btn btn-success">
                                Agregar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion MODAL mdl_addtorneo ?>            
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<?php #region region JS date ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/js/datepickerini.js"></script>
<?php #endregion JS date ?>
<!-- BEGIN JS autocomplete -->
<script type="text/javascript">
  $("#source").autocomplete({
    source: [
        <?php foreach ($sources as $source): ?>
            "<?php echo $source->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN JS autocomplete -->
<script type="text/javascript">
  $("#channel").autocomplete({
    source: [
        <?php foreach ($channels as $channel): ?>
            "<?php echo $channel->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN JS autocomplete -->
<script type="text/javascript">
  $("#equipo_home").autocomplete({
    source: [
        <?php foreach ($equipos as $equipo): ?>
            "<?php echo $equipo->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN JS autocomplete -->
<script type="text/javascript">
  $("#equipo_away").autocomplete({
    source: [
        <?php foreach ($equipos as $equipo): ?>
            "<?php echo $equipo->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN JS autocomplete -->
<script type="text/javascript">
  $("#torneo").autocomplete({
    source: [
        <?php foreach ($torneos as $torneo): ?>
            "<?php echo $torneo->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN JS autocomplete -->
<script type="text/javascript">
  $("#stakazo").autocomplete({
    source: [
        <?php foreach ($stakazos as $stakazo): ?>
            "<?php echo $stakazo->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<?php #region region JS modal text focus when shown ?>
<script type="text/javascript">
    $('#mdl_addequipo').on('shown.bs.modal', function () {
        $('#addequipo_nombre').trigger('focus');
    });
</script>
<?php #region JS modal text focus when shown ?>
<?php #region region JS modal text focus when shown ?>
<script type="text/javascript">
    $('#mdl_addtorneo').on('shown.bs.modal', function () {
        $('#addtorneo_nombre').trigger('focus');
    });
</script>
<?php #region JS modal text focus when shown ?>
<?php #region region time js ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js"></script>
<script>
    $(".timepicker").timepicker({
        showMeridian: false,  // 24-hour format
        defaultTime: false,   // Keeps the input empty until the user selects a time
        minuteStep: 1         // Step for minutes selection
    });
</script>
<?php #endregion time js ?>
<?php #endregion JS ?>

</body>
</html>