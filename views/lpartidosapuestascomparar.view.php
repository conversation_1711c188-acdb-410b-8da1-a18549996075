<?php
#region region DOCS
/** @var PartidoApuestaComparar[] $partidosapuestas_totalcorners */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Partidos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">Apuestas a comparar</h1>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="lpartidosapuestascomparar" method="POST">
            <input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">

            <!-- ROW -->
            <div class="row mt-3">
                <!-- LINK -->
                <div class="col-md-12 col-xs-12">
                    <a href="#mdl_deleteall" class="region_LINK_deleteall btn btn-danger w-100" data-bs-toggle="modal">
                        Eliminar todos
                    </a>
                </div>
                <!-- END LINK -->   
            </div>
            <!-- END ROW -->
            <!-- BEGIN navtab -->
            <ul class="region_NAVTAB_HEAD nav nav-tabs fs-15px mt-3">
                <li class="nav-item" onclick="tabselect(1)">
                    <a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">
                        Total corners
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(2)">
                    <a href="#default-tab-2" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 2) ? "active" : ""; ?>">
                        Mas corners
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(3)">
                    <a href="#default-tab-3" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 3) ? "active" : ""; ?>">
                        Corners de HOME
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(4)">
                    <a href="#default-tab-4" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 4) ? "active" : ""; ?>">
                        Corners de AWAY
                    </a>
                </li>
            </ul>
            <div class="tab-content panel rounded-0 rounded-bottom">
                <!-- BEGIN NAVTAB1 totalcorners -->
                <div class="region_NAVTAB_totalcorners tab-pane fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">
                    <!-- TABLE -->
                    <table class="region_TABLE_totalcorners table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Home</th>
                            <th class="text-center">Away</th>
                            <th class="text-center">Pais</th>
                            <th class="text-center">Apuesta</th>
                            <th class="text-center">Valor</th>
                            <th class="text-center">H @H vs All</th>
                            <th class="text-center">A @A vs All</th>
                            <th class="text-center">H @H vs A</th>
                        </thead>
                        <tbody class="fs-10px">
                        <?php foreach ($partidosapuestas_totalcorners as $partidoapuesta_totalcorners): ?>
                            <tr>
                                <td class="text-center"><?php echo $partidoapuesta_totalcorners->partido->home; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_totalcorners->partido->away; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_totalcorners->partido->pais; ?></td>
                                <td><?php echo $partidoapuesta_totalcorners->apuestatipo->nombre; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_totalcorners->valorapuesta; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_totalcorners->porccrit5; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta_totalcorners->porccrit9; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta_totalcorners->porccrit2; ?>%</td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->
                </div>
                <!-- END NAVTAB1 totalcorners -->
                <!-- BEGIN mascorners -->
                <div class="region_NAVTAB_mascorners tab-pane fade <?php echo ($tabselected == 2) ? "active show" : ""; ?>" id="default-tab-2">
                    <!-- TABLE -->
                    <table class="region_TABLE_totalcorners table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Home</th>
                            <th class="text-center">Away</th>
                            <th class="text-center">Apuesta</th>
                            <th class="text-center">Valor</th>
                            <th class="text-center">H @H vs All</th>
                            <th class="text-center">A @A vs All</th>
                            <th class="text-center">H @H vs A</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php foreach ($partidosapuestas_mascorners as $partidoapuesta_mascorners): ?>
                            <tr>
                                <td class="text-center"><?php echo $partidoapuesta_mascorners->partido->home; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_mascorners->partido->away; ?></td>
                                <td><?php echo $partidoapuesta_mascorners->apuestatipo->nombre; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_mascorners->valorapuesta; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_mascorners->porccrit5; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta_mascorners->porccrit9; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta_mascorners->porccrit2; ?>%</td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->
                </div>
                <!-- END mascorners -->
                <!-- BEGIN NAVTAB cornershome -->
                <div class="region_NAVTAB_cornershome tab-pane fade <?php echo ($tabselected == 3) ? "active show" : ""; ?>" id="default-tab-3">
                    <!-- TABLE -->
                    <table class="region_TABLE_totalcorners table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Home</th>
                            <th class="text-center">Away</th>
                            <th class="text-center">Apuesta</th>
                            <th class="text-center">Valor</th>
                            <th class="text-center">H @H vs All</th>
                            <th class="text-center">A @A vs All</th>
                            <th class="text-center">H @H vs A</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php foreach ($partidosapuestas_cornershome as $partidoapuesta_cornershome): ?>
                            <tr>
                                <td class="text-center"><?php echo $partidoapuesta_cornershome->partido->home; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_cornershome->partido->away; ?></td>
                                <td><?php echo $partidoapuesta_cornershome->apuestatipo->nombre; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_cornershome->valorapuesta; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_cornershome->porccrit5; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta_cornershome->porccrit9; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta_cornershome->porccrit2; ?>%</td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->
                </div>
                <!-- END NAVTAB cornershome -->  
                <!-- BEGIN NAVTAB cornersaway -->
                <div class="region_NAVTAB_cornersaway tab-pane fade <?php echo ($tabselected == 4) ? "active show" : ""; ?>" id="default-tab-4">
                    <!-- TABLE -->
                    <table class="region_TABLE_totalcorners table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Home</th>
                            <th class="text-center">Away</th>
                            <th class="text-center">Apuesta</th>
                            <th class="text-center">Valor</th>
                            <th class="text-center">H @H vs All</th>
                            <th class="text-center">A @A vs All</th>
                            <th class="text-center">H @H vs A</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php foreach ($partidosapuestas_cornersaway as $partidoapuesta_cornersaway): ?>
                            <tr>
                                <td class="text-center"><?php echo $partidoapuesta_cornersaway->partido->home; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_cornersaway->partido->away; ?></td>
                                <td><?php echo $partidoapuesta_cornersaway->apuestatipo->nombre; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_cornersaway->valorapuesta; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta_cornersaway->porccrit5; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta_cornersaway->porccrit9; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta_cornersaway->porccrit2; ?>%</td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->
                </div>
                <!-- END NAVTAB cornersaway -->              
            </div>
            <!-- END navtab -->
            <!-- BEGIN mdl_deleteall -->
            <div class="region_MODAL_mdl_deleteall modal fade" id="mdl_deleteall">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <input type="hidden" id="mdl_deleteall_DATA_ID" name="mdl_deleteall_DATA_ID">

                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar todo</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Esta seguro que desea eliminar todo?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                Cancelar
                            </a>
                            <button type="submit" id="sub_deleteall" name="sub_deleteall" class="btn btn-danger">
                                Confirmar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END mdl_deleteall -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script id="region_JS" src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<!-- BEGIN JS tabselect -->
<script id="region_JS_tabselect" type="text/javascript">
    function tabselect(ntab) {
        document.getElementById('tabselected').value = ntab;
    }
</script>
<!-- END JS tabselect -->
<!-- BEGIN JS MODAL mdl_deleteall -->
<script id="region_JS_MODAL_mdl_deleteall" type="text/javascript">
    $('#mdl_deleteall').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_DATA_ID = button.data('DATA_ID');

        const mdl_deleteall_DATA_ID = document.getElementById('mdl_deleteall_DATA_ID');

        mdl_deleteall_DATA_ID.value = recipient_DATA_ID;
    })
</script>
<!-- END JS MODAL mdl_deleteall -->
<?php #endregion JS ?>

</body>
</html>