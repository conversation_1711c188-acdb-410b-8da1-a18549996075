<?php
#region region DOCS
/** @var int $count_odds_por_revisar */
/** @var float $min_perc_kelly_crit */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Apuestas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region region PAGE HEADER ?>
        <h4>Generar apuestas</h4>

        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region FORM ?>
        <form action="gapuestas" method="POST">
            <input type="hidden" id="min_perc_kelly_crit" name="min_perc_kelly_crit" value="<?php echo @recover_var($min_perc_kelly_crit) ?>">
            <!-- BEGIN row -->
            <div class="row mt-3">
                <div class="col-md-12 col-xs-12">
                    <span class="badge bg-primary rounded-0 fs-12px">
                        Partidos incluidos: <?php echo $count_odds_por_revisar; ?>
                    </span>
                </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <?php #region region SUBMIT sub_generar ?>
                <div class="col-md-8 col-xs-12">
                    <button type="submit" id="sub_generar" name="sub_generar" class="btn btn-xs btn-success w-100 no-border-radious">
                        Generar
                    </button>
                </div>
                <?php #endregion SUBMIT sub_generar ?>
                <?php #region region LINK regresar ?>
                <div class="col-md-4 col-xs-12">
                    <a href="lpartidos" class="btn btn-xs btn-default w-100 no-border-radious">
                        Regresar
                    </a>
                </div>
                <?php #endregion LINK regresar ?>
            </div>
            <!-- END row -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $success_text; ?>",
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>

<?php #endregion JS ?>

</body>
</html>