<?php
#region region DOCS
/** @var BtApuesta $sel_apuesta */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Apuestas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">

        <!-- BEGIN page-header -->
        <h4>Compartir apuesta</h4>
        
        <hr>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="sapuesta" method="POST">
            <input type="hidden" id="id_apuesta" name="id_apuesta" value="<?php echo @recover_var($id_apuesta) ?>">
            
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN link -->
                <div class="col-md-12 col-xs-12">
                    <a href="lapuestas" class="btn btn-xs btn-default w-100 no-border-radious">
                        Regresar
                    </a>
                </div>
                <!-- END link -->
            </div>
            <!-- END ROW -->
            <!-- PANEL -->
            <div class="panel panel-inverse mt-3 no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">
                        Compartir:
                    </h4>
                    <?php #region region SUBMIT sub_copiar ?>
                    <button id="sub_copiar" class="btn btn-xs btn-primary no-border-radious">
                        Copiar
                    </button>
                    <?php #endregion SUBMIT sub_copiar ?>   
                    <?php #region region SUBMIT sub_marcar_publicado ?>
                    <button id="sub_marcar_publicado" name="sub_marcar_publicado" class="btn btn-xs btn-primary no-border-radious ms-2">
                        Publicar
                    </button>
                    <?php #endregion SUBMIT sub_marcar_publicado ?>                 
                </div>
                <!-- BEGIN PANEL body -->
                <div>
                    <!-- BEGIN textarea -->
                    <div class="col-md-12 col-xs-12">
                        <textarea rows="10" name="mensaje_compartir" id="mensaje_compartir" class="form-control form-control-fh fs-12px no-border-radious"><?php
                                if (ordena($sel_apuesta->channel->id) == Channel::ID_FREE) {
                                    echo "⚠️ <b>Apuesta FREE</b> ⚠️\n\n";
                                } else {
                                    echo "⚠️💎 <b>Apuesta PREMIUM</b> 💎⚠️\n\n";
                                }

                                echo "🏆 <b>" . ucwords(mb_strtolower($sel_apuesta->torneo->nombre)) . "</b>\n";
                                echo "⚽️ <b>" . ucwords(mb_strtolower($sel_apuesta->equipo_home->nombre)) . "</b> -VS- <b>" . ucwords(mb_strtolower($sel_apuesta->equipo_away->nombre)) . "</b>\n";
                                echo "⏰ " . $sel_apuesta->fecha_partido_texto . "\n\n";
                                
                                echo "🏁 <b>" . ucwords(mb_strtolower($sel_apuesta->tipo_apuesta)) . ":</b> ";
                                echo ucwords(mb_strtolower($sel_apuesta->tipo_apuesta_add_texto)) . " ";
                                echo ucwords(mb_strtolower($sel_apuesta->tipo_apuesta_add2)) . "\n\n";
                                echo "💰 Cuota: " . ucwords(mb_strtolower($sel_apuesta->cuota_grupo)) . " | ";
                                echo "📊 STAKE: " . ucwords(mb_strtolower($sel_apuesta->stake)) . " = " . ucwords(mb_strtolower($sel_apuesta->stake)) . "% de la cuenta de apuestas\n\n";

                                /* if (ordena($sel_apuesta->channel->id) == Channel::ID_FREE) {
                                    echo "📊 STAKE: " . ucwords(mb_strtolower($sel_apuesta->stake)) . " = " . ucwords(mb_strtolower($sel_apuesta->stake)) . "%\n";
                                } else {
                                    echo "📊 Probabilidades: " . ucwords(mb_strtolower($sel_apuesta->probabilidades)) . "%\n";
                                    echo "🏦 Bankroll % sugerido: " . ucwords(mb_strtolower($sel_apuesta->bankroll)) . "%\n";
                                } */
                                
                                echo "🔞 SOLO +18. Apuesta con RESPONSABILIDAD.";
                            ?>
                        </textarea>
                    </div>
                    <!-- END textarea -->                    
                </div>
                <!-- END PANEL body -->
            </div>
            <!-- END PANEL -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<script>
    document.getElementById('sub_copiar').addEventListener('click', function(event) {
        // Prevent the form from submitting
        event.preventDefault();

        // Select the inner div by its ID
        var contentDiv = document.getElementById('mensaje_compartir');

        // Get the content as text
        var contentText = contentDiv.innerText;

        // Copy the content to clipboard
        navigator.clipboard.writeText(contentText).then(function() {
        console.log('Content copied to clipboard!');
        }).catch(function(err) {
        console.error('Error in copying text: ', err);
        });
    });
</script>
<?php #endregion JS ?>

</body>
</html>