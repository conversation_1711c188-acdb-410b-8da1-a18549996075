<?php
#region region DOCS
/** @var TickerPosicion $new_ticker_posicion */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Positions</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h4>Agregar posicion: <?php echo $sel_ticker->nombre; ?></h4>
        
        <hr>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="iticker_posicion" method="POST">
            <input type="hidden" id="id_ticker" name="id_ticker" value="<?php echo @recover_var($id_ticker) ?>">

            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN date -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                            Fecha:
                        </span>
                        <input type="text" id="fecha" name="fecha" value="<?php echo @recover_var($new_ticker_posicion->fecha) ?>" class="form-control form-control-fh fs-12px datepicker no-border-radious" autocomplete="off"/>
                    </div>
                </div>
                <!-- END date -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                            Avg. price:
                        </span>
                        <input type="text" name="avg_price" id="avg_price" value="<?php echo @recover_var($new_ticker_posicion->avg_price) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                            Stoploss:
                        </span>
                        <input type="text" name="stoploss" id="stoploss" value="<?php echo @recover_var($new_ticker_posicion->stoploss) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                            Profit:
                        </span>
                        <input type="text" name="profit" id="profit" value="<?php echo @recover_var($new_ticker_posicion->profit) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                            Position size:
                        </span>
                        <input type="text" name="position_size" id="position_size" value="<?php echo @recover_var($new_ticker_posicion->position_size) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('');"/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN link -->
                <div class="col-md-4 col-xs-12">
                    <a href="ltickers" class="region_LINK_regresar btn btn-xs btn-default w-100 no-border-radious">
                        Regresar
                    </a>
                </div>
                <!-- END link -->
                <?php #region region SUBMIT sub_agregar ?>
                <div class="col-md-8 col-xs-12">
                    <button type="submit" id="sub_agregar" name="sub_agregar" class="region_SUBMIT_sub_agregar btn btn-xs btn-success w-100 no-border-radious">
                        Agregar
                    </button>
                </div>
                <?php #endregion SUBMIT sub_agregar ?>
            </div>
            <!-- END ROW -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script id="region_JS" src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region JS date ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/js/datepickerini.js"></script>
<?php #endregion JS date ?>
<?php #endregion JS ?>

</body>
</html>