<!DOCTYPE html>
<html lang="en"  class="dark-mode">
<head>
	<meta charset="utf-8" />
	<title>My Dash | Notes</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
	<meta content="" name="description" />
	<meta content="" name="author" />
	
	<!-- #head -->
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
</head>
<body>
	<!-- BEGIN #loader -->
	<div id="loader" class="app-loader">
		<span class="spinner"></span>
	</div>
	<!-- END #loader -->

	<!-- BEGIN #app -->
    <div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
		<!-- #header -->
		<?php require_once __ROOT__ . '/views/header.view.php'; ?>

		<!-- #sidebar -->
		<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php require_once __ROOT__ . '/views/labels.view.php'; ?>
			
			<!-- BEGIN page-header -->
			<h1 class="page-header">Notes</h1>
			<!-- END page-header -->
			
			<form action="inotes" method="POST">
				<!-- BEGIN row -->
				<div class="row">
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px text-lowercase" name="title" id="title" value="<?php echo @recover_var($title) ?>" autofocus/>
							<label for="title" class="d-flex align-items-center fs-15px">
								Title:
							</label>
						</div>
					</div>
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px text-lowercase" name="tags" id="tags" value="<?php echo @recover_var($tags) ?>"/>
							<label for="tags" class="d-flex align-items-center fs-15px">
								Tags:
							</label>
						</div>
					</div>
				</div>
				<!-- END row -->
				<!-- BEGIN row -->
				<div class="row mt-3">
					<div class="col">
						<div class="form-floating">
							<textarea type="text" class="form-control fs-15px" name="detail" id="detail" rows="18"></textarea>
							<label for="detail" class="d-flex align-items-center fs-15px form-textarea">
								Detail:
							</label>
						</div>
					</div>
				</div>
				<!-- END row -->
				<!-- BEGIN row -->
				<div class="row mt-3">
                    <div class="col">
                        <a href="lnotes" class="btn btn-default w-100">
                            Regresar
                        </a>
                    </div>
                    <div class="col-8">
						<button type="submit" id="sub_add" name="sub_add" class="btn btn-success w-100">
							Crear
						</button>
					</div>
				</div>
				<!-- END row -->
			</form>
		</div>
		<!-- END #content -->
		
		<!-- BEGIN scroll-top-btn -->
		<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
	</div>
	<!-- END #app -->

	<!-- ================== BEGIN core-js ================== -->
	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
	<!-- ================== END core-js ================== -->

</body>
</html>