<?php
#region region DOCS
/** @var Deuda[] $deudas */
/** @var float $valortotal */
/** @var Budget[] $budgets */
/** @var float $disponible */
/** @var array $disponiblesxdias */
/** @var string $tabselected */
/** @var Simulacion $newsimulacion */
/** @var Simulacion[] $simulaciones */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Deudas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <?php #region region FORM ?>
        <form action="sdeudas" method="POST">
            <input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">

            <?php #region region NAVTAB header ?>
            <ul class="nav nav-tabs fs-15px">
                <li class="nav-item" onclick="tabselect(1)">
                    <a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">
                        Simulaciones
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(2)">
                    <a href="#default-tab-2" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 2) ? "active" : ""; ?>">
                        Deudas
                    </a>
                </li>
            </ul>
            <div class="tab-content panel p-3 rounded-0 rounded-bottom">
                <?php #region region TAB simulaciones ?>
                <div class="tab-pane fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">
                    <!-- BEGIN row -->
                    <div class="row mt-3">
                        <!-- BEGIN text -->
                        <div class="col-md-6 col-xs-12">
                            <div class="form-floating">
                                <input type="text" class="form-control fs-15px text-uppercase" name="nombresimulacion" id="nombresimulacion" value="<?php echo @recover_var($newsimulacion->nombre) ?>" placeholder="Nombre:" autofocus/>
                                <label for="nombresimulacion" class="d-flex align-items-center fs-15px">
                                    Nombre:
                                </label>
                            </div>
                        </div>
                        <!-- END text -->
                        <!-- BEGIN text -->
                        <div class="col-md-6 col-xs-12">
                            <div class="form-floating">
                                <input type="text" class="form-control fs-15px" name="valorsimulacion" id="valorsimulacion" value="<?php echo @recover_var($newsimulacion->valor) ?>" placeholder="Valor:"/>
                                <label for="valorsimulacion" class="d-flex align-items-center fs-15px">
                                    Valor:
                                </label>
                            </div>
                        </div>
                        <!-- END text -->
                    </div>
                    <!-- END row -->
                    <!-- BEGIN row -->
                    <div class="row mt-3">
                        <div class="col-md-4 col-xs-12">
                            <a href="#mdl_delallsimulacion" class="btn btn-danger w-100" data-bs-toggle="modal">
                                Eliminar todo
                            </a>
                        </div>
                        <?php #region region SUBMIT sub_addsimulacion ?>
                        <div class="col-md-8 col-xs-12">
                            <button type="submit" id="sub_addsimulacion" name="sub_addsimulacion" class="btn btn-success w-100">
                                Agregar
                            </button>
                        </div>
                        <?php #endregion sub_addsimulacion ?>
                    </div>
                    <!-- END row -->
                    <?php #region region TABLE simulaciones ?>
                    <table class="table table-hover table-sm mt-3">
                        <thead>
                        <tr>
                            <th class="w-20px"></th>
                            <th>Nombre</th>
                            <th>Valor</th>
                        </tr>
                        </thead>
                        <tbody class="fs-14px">
                        <?php #region region ARRAY simulaciones ?>
                        <?php foreach ($simulaciones as $simulacion): ?>
                            <tr>
                                <td>
                                    <i class="fa fa-trash fa-md cursor-pointer text-danger ms-1" data-bs-toggle="modal" data-bs-target="#mdl_delsimulacion" data-idsimulacion="<?php echo limpiar_datos($simulacion->id) ?>"></i>
                                </td>
                                <td><?php echo $simulacion->nombre; ?></td>
                                <td class="text-end"><?php echo formatCurrencyConSigno($simulacion->valor); ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <?php #endregion array simulaciones ?>
                        </tbody>
                    </table>
                    <?php #endregion table simulaciones ?>
                </div>
                <?php #endregion tab simulaciones ?>
                <?php #region region TAB deudas ?>
                <div class="tab-pane fade <?php echo ($tabselected == 2) ? "active show" : ""; ?>" id="default-tab-2">
                    <?php #region region ARRAY canales ?>
                    <?php foreach ($budgets as $budget): ?>
                        <!-- BEGIN row -->
                        <div class="row mt-2">
                            <div class="col">
                                <?php #region region BAR canal ?>
                                <div class="progress">
                                    <div class="progress-bar bg-primary fs-10px fw-bold" style="width: <?php echo $budget->porcentaje; ?>%">
                                        <span><?php echo $budget->canal; ?>: $<?php echo format_currency($budget->valor); ?></span>
                                    </div>
                                </div>
                                <?php #endregion BAR canales ?>
                            </div>
                        </div>
                        <!-- END row -->
                    <?php endforeach; ?>
                    <?php #endregion ARRAY canales ?>
                    <?php #region region ARRAY disponible ?>
                    <?php foreach ($disponiblesxdias as $disponiblexdias): ?>
                        <!-- BEGIN row -->
                        <div class="row mt-2">
                            <div class="col">
                                <?php #region region BAR disponible ?>
                                <div class="progress">
                                    <div class="progress-bar bg-indigo fs-10px fw-bold" style="width: <?php echo $disponiblexdias['porcentajedisponible']; ?>%">
                                        <span>
                                            DISPONIBLE vs <?php echo $disponiblexdias['dias']; ?> DIAS: $<?php echo format_currency($disponiblexdias['disponible']); ?>
                                            / SIMULACIONES: <?php echo formatCurrencyConSigno($disponiblexdias['valtotalsimulaciones']); ?>
                                        </span>
                                    </div>
                                    <div class="progress-bar <?php echo $disponiblexdias['estadobarraxdias']; ?> fs-10px fw-bold" style="width: <?php echo $disponiblexdias['porcentajedeudaxdias']; ?>%">
                                        <?php echo formatCurrencyConSigno($disponiblexdias['disponiblesobraxdias']); ?>
                                    </div>
                                </div>
                                <?php #endregion BAR disponible ?>
                            </div>
                        </div>
                        <!-- END row -->
                    <?php endforeach; ?>
                    <?php #endregion ARRAY disponible ?>
                    <?php #region region TABLE deudas ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="w-20px"></th>
                            <th class="text-center">Dias<br>para pagar</th>
                            <th class="text-center">Nombre</th>
                            <th class="text-center">Valor</th>
                            <th class="text-center">Disponible</th>
                            <th class="text-center">Fecha<br>a pagar</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php #region region ARRAY deudas ?>
                        <?php foreach ($deudas as $deuda): ?>
                            <tr>
                                <td>
                                    <?php if ($deuda->nomoneyleftwotrk == 1): ?>
                                        <i class="fa fa-ban fa-md text-warning ms-1"></i>
                                    <?php endif; ?>
                                    <?php if ($deuda->nomoneyleftwtrk == 1): ?>
                                        <i class="fa fa-ban fa-md text-danger ms-1"></i>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center"><?php echo $deuda->diasparapagar; ?></td>
                                <td><?php echo $deuda->nombre; ?></td>
                                <td class="text-end"><?php echo formatCurrencyConSigno($deuda->valor); ?></td>
                                <td class="text-end <?php echo ($deuda->disponibleleft < 0) ? "text-danger" : ""; ?>"><?php echo formatCurrencyConSigno($deuda->disponibleleft); ?></td>
                                <td class="text-center"><?php echo formatDatewDayWeek($deuda->fechaapagar); ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <?php #endregion array deudas ?>
                        </tbody>
                    </table>
                    <?php #endregion table deudas ?>
                </div>
                <?php #endregion tab deudas ?>
            </div>
            <?php #endregion navtab ?>
            <?php #region region MODAL mdl_delsimulacion ?>
            <div class="modal fade" id="mdl_delsimulacion">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <input type="hidden" id="mdl_delsimulacion_idsimulacion" name="mdl_delsimulacion_idsimulacion">

                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar simulacion</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Esta seguro que desea eliminar esta simulacion?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                            </a>
                            <button type="submit" id="sub_delsimulacion" name="sub_delsimulacion" class="btn btn-danger">
                                <i class="fa fa-check fa-lg fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion mdl_delsimulacion ?>
            <?php #region region MODAL mdl_delallsimulacion ?>
            <div class="modal fade" id="mdl_delallsimulacion">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar simulacion</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Esta seguro que desea eliminar todas las simulaciones?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                            </a>
                            <button type="submit" id="sub_delallsimulacion" name="sub_delallsimulacion" class="btn btn-danger">
                                <i class="fa fa-check fa-lg fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion mdl_delallsimulacion ?>
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region JS tabselect ?>
<script type="text/javascript">
    function tabselect(ntab) {
        document.getElementById('tabselected').value = ntab;
    }
</script>
<?php #endregion js tabselect ?>
<?php #region region JS mdl_delsimulacion ?>
<script type="text/javascript">
    $('#mdl_delsimulacion').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_idsimulacion = button.data('idsimulacion');

        const mdl_delsimulacion_idsimulacion = document.getElementById('mdl_delsimulacion_idsimulacion');

        mdl_delsimulacion_idsimulacion.value = recipient_idsimulacion;
    })
</script>
<?php #endregion js mdl_delsimulacion ?>
<?php #endregion js ?>

</body>
</html>