CKEDITOR.plugins.setLang("find","ro",{find:"<PERSON><PERSON><PERSON><PERSON><PERSON>",findOptions:"Find Options",findWhat:"Găseşte:",matchCase:"Deosebeşte majuscule de minuscule (Match case)",matchCyclic:"Potrivește ciclic",matchWord:"Doar cuvintele întregi",notFoundMsg:"Textul specificat nu a fost găsit.",replace:"Înlocuieşte",replaceAll:"Înlocuieşte tot",replaceSuccessMsg:"%1 căutări înlocuite.",replaceWith:"Înlocuieşte cu:",title:"G<PERSON><PERSON>şte şi înlocuieşte"});