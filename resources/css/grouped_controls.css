.btn-group-toggle > .btn input[type="radio"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none;
}

.btn-group-toggle > .btn {
    border-radius: 0;
    border: 1px solid black;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.btn-group {
    display: flex;
}

.btn-group-toggle > .btn:first-child {
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem;
}

.btn-group-toggle > .btn:last-child {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem;
}
.btn-group-toggle > .btn.active {
    background-color: #007bff !important;
    color: white !important;
}