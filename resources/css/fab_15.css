body {
    /*
	font-family: 'Roboto' !important;
	font-family: 'Ubuntu Mono', monospace;
	font-family: 'Open Sans', sans-serif;
	font-family: "Montserrat", sans-serif;
	font-family: "Lexend Deca", sans-serif;
	font-family: "Lato", sans-serif;
	*/
	font-family: "Quicksand", sans-serif;
	font-optical-sizing: auto;
	font-weight: 400;
	font-style: normal;
}

.app-header .navbar-brand{
	font-weight: 400 !important;
}

.semi-bold {
    font-weight: 600 !important;
    cursor: pointer;
}

.form-check-input {
    cursor: pointer;
}

.pointer {
    cursor: pointer;
}

.move-right {
    text-align: right;
    float: right;
}

#gaming .widget-list-media, 
#gaming .widget-list-content, 
#gaming .widget-list-action {
    border-top: 1px solid var(--app-component-border-color);
}

.note .alert-link{
    color: #223112;
}

.widget-list .widget-list-item .widget-list-action, .widget-list .widget-list-item .widget-list-content, .widget-list .widget-list-item .widget-list-media {
	padding: .3rem 0.5rem;
}

.widget-list .widget-list-item .widget-list-desc {
    font-size: 1em !important;
}

.spacing-1em{
	letter-spacing: .1em !important;
}

.spacing-05em{
	letter-spacing: .05em !important;
}

.spacing-08em{
	letter-spacing: .08em !important;
}

.underlinehover:hover{
	text-decoration: underline;
}

/*
.panel .panel-body, .widget-list {
    background-color: var(--bs-gray-800) !important;
}

.widget-list .widget-list-item, .table {
    color: var(--bs-gray-100) !important;
}
*/

th {
    font-size: 12px !important;
}

.form-floating>.form-textarea,
.form-floating>textarea
{
	height: inherit !important;
}

.w-10px{
	width: 10px !important;
}

.w-20px{
	width: 20px !important;
}

.w-30px{
	width: 30px !important;
}

.w-40px{
	width: 40px !important;
}

.w-60px{
	width: 60px !important;
}

.w-80px{
	width: 80px !important;
}

.w-100px{
	width: 100px !important;
}

.w-120px{
	width: 120px !important;
}

.w-140px{
	width: 140px !important;
}

.w-150px{
	width: 150px !important;
}

.w-200px{
	width: 200px !important;
}

.form-floating .form-control,
table td {
    letter-spacing: .13em;
}

.btn-lg {
    letter-spacing: .15em;
}

.underline{
	text-decoration: underline;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6, .form-control, .form-select{
	font-weight: 400 !important;
}

option {
	font-size: 12px !important;
}

.form-select {
	cursor: pointer;
}

/* BEGIN form w autocomplete style */
span.select2-selection.select2-selection--single{
	border-radius: 0 !important;
	border-color: transparent !important;
}
span.select2.select2-container{
	position: relative !important;
	flex: 1 1 auto  !important;
	width: 1%  !important;
	min-width: 0  !important;
}
/* END form w autocomplete style */

.btn .fa-sm {
    vertical-align: 0em !important;
}

.btn .fa-lg {
	vertical-align: -.15em !important;
    padding-left: 1px !important;
}

.btn-inline{
	line-height: 3.5 !important;
}

hr{
	color: #ffffff !important;
}

/*
.tab-content.panel.p-3.rounded-0.rounded-bottom{
	background: #2d353c !important;
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active{
	background-color: #2d353c !important;
	color: white !important;
}
*/

textarea.bordered{
	border-radius: 10px !important;
}

.progress-boss{
	height: 10px !important;
}

.stats-title-boss{
	font-size: 1.3em !important;
}

.stats-desc-boss{
	font-size: 1.1em !important;
}

.boss{
	cursor: pointer;
}

.progress-bar span
{
	position: absolute;
	padding-left:5px;
}

.dark-mode .table{
	--bs-table-hover-bg: rgba(73, 80, 87, 0.35) !important;	
}

.border-top-3px {
    border-top: 3px solid #ced4da !important;
}

.border-bottom-3px {
	border-bottom: 3px solid #ced4da !important;
}

.border-start-2px {
	border-left: 2px solid #ced4da !important;
}

.border-end-2px {
	border-right: 2px solid #ced4da !important;
}

.card.bg-inverse:hover{
	opacity: 0.7 !important;
}

.lh-03{
	line-height: 0.3!important;
}

/* BEGIN grafica css */
.tick text {
	font-size: .8em !important;
}
/* BEGIN grafica css */

/* BEGIN datepicker */
body .datepicker.dropdown-menu {
	min-width: 300px !important;
}
.datepicker {
	padding: .4375rem .75rem !important;
}
/* END datepicker */

/* BEGIN floating button */
.floating-button{
	position:fixed;
	bottom:20px;
	right:52px;
}
/* END floating button */

/* BEGIN textbox css */
.form-control-fh{
	padding: 6px !important;
	padding-left: 7px !important;
	border-color: var(--app-component-bg) !important;
}

.tab-pane .form-control-fh,
.tab-pane .form-control-fh:focus,
.tab-pane .select2.select2-container .selection .select2-selection,
.panel-body .form-control-fh,
.panel-body .form-control-fh:focus,
.accordion-collapse .form-control-fh,
.accordion-collapse .form-control-fh,
.modal-body .form-control-fh
{
	background-color: var(--bs-body-bg) !important;
	border-color: var(--bs-body-bg) !important;
}

.form-control-fh:focus {
    box-shadow: unset !important;
}

.no-border-radious{
	border-radius: 0 !important;
}
/* END textbox css */

/* BEGIN button/submit css */
.no-border-radious{
	border-radius: 0 !important;
}
.btn.btn-xs {
    height: 29px !important;
}
.btn-table{
	height: 15px !important;
    font-size: .9em !important;
    padding: 0.5px !important;
}
.input-group-btn {
	padding-top: 1px !important;
}
/* END button/submit css */

/* BEGIN link css */
a.no-border-radious{
	padding: 5px !important;
}
/* END link css */

/* BEGIN navtab css */
ul.no-border-radious .nav-link,
.no-border-radious div.panel-heading,
.no-border-radious .accordion-item .accordion-collapse,
.no-border-radious .accordion-item:first-of-type .accordion-button {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
}
/* END navtab css */
/* BEGIN table css */
tr.table-highlight{
	background-color: #173f51 !important;
}
.table-transparent-border{
	border-color: #49505700 !important;
}
.dark-mode .table {
	--bs-table-hover-color: inherit !important;
}
/* END table css */

/* BEGIN borders */
.border-start-success{
	border-left-color: #00acac !important;
    border-left-width: 6px !important;	
}
.border-start-warning{
	border-left-color: rgba(var(--bs-warning-rgb),var(--bs-bg-opacity))!important;
    border-left-width: 6px !important;	
}
.border-start-danger{
	border-left-color: rgba(var(--bs-danger-rgb),var(--bs-bg-opacity))!important;
    border-left-width: 6px !important;	
}
/* END borders */

/* BEGIN cke editor */
/*.cke .cke_top {
	background: #7b8b9a !important;
}
.cke_editable {
	background-color: #6c757d !important;
}*/
body.cke_editable.cke_editable_themed.cke_contents_ltr.cke_show_borders {
	line-height: 0.5 !important;
}
/* END cke editor */

input.timepicker
,.bootstrap-timepicker{
	height: 33.3px !important;
}

.fa-1_5x {
    font-size: 1.5em;
}

/* region region input file */
.custom-file-input {
	position: relative;
	width: 100%;
	height: 40px;
	border: 1px solid #ccc;
	border-radius: 5px;
	padding: 5px;
	cursor: pointer;
}

.custom-file-input input[type="file"] {
	position: absolute;
	left: 0;
	top: 0;
	opacity: 0;
	width: 100%;
	height: 100%;
	cursor: pointer;
}

.custom-file-input::before {
	content: attr(data-placeholder);
	display: block;
	width: 100%;
	height: 100%;
	background-color: #2d353c;
	padding-left: 10px;
	line-height: 30px;
	color: white;
	pointer-events: none;
}
/* endregion input file */

.tooltip-copied {
	position: absolute;
	background-color: #007bff;
	color: #fff;
	padding: 5px 10px;
	border-radius: 5px;
	opacity: 0;
	transition: opacity 0.3s ease-in-out;
}

/* region region tooltip copied text */
.tooltip-copied-text {
	position: absolute;
	background-color: #333;
	color: #fff;
	padding: 5px 10px;
	border-radius: 4px;
	font-size: 12px;
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}
.tooltip-copied-text.show {
	opacity: 1;
}
/* endregion tooltip copied text */

.bar-canales{
	cursor: pointer;
}