<?php

require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';

class PartidoApuestaComparar
{
    public string $id;
    public string $idpartido;
    public Partido $partido;
    public string $idapuestatipo;
    public ApuestaTipo $apuestatipo;
    public float $valorapuesta;
    public float $porccrit1;
    public float $porccrit2;
    public float $porccrit3;
    public float $porccrit4;
    public float $porccrit5;
    public float $porccrit6;
    public float $porccrit7;
    public float $porccrit8;
    public float $porccrit9;
    public int $estado;
    private string $bd_table = 'partidos_apuestas_comparar';
    private string $bd_alias = 'parapcom';
    private string $bd_id = 'id_partido_apuesta_comparar';
    private string $bd_idpartido = 'id_partido';
    private string $bd_idapuestatipo = 'id_apuestatipo';
    private string $bd_valorapuesta = 'valor_apuesta';
    private string $bd_porccrit1 = 'porc_crit1';
    private string $bd_porccrit2 = 'porc_crit2';
    private string $bd_porccrit3 = 'porc_crit3';
    private string $bd_porccrit4 = 'porc_crit4';
    private string $bd_porccrit5 = 'porc_crit5';
    private string $bd_porccrit6 = 'porc_crit6';
    private string $bd_porccrit7 = 'porc_crit7';
    private string $bd_porccrit8 = 'porc_crit8';
    private string $bd_porccrit9 = 'porc_crit9';
    private string $bd_estado = 'estado';


    function __construct()
    {
        $this->id = '';
        $this->idpartido = '';
        $this->partido = new Partido;
        $this->idapuestatipo = '';   
        $this->apuestatipo = new ApuestaTipo;     
        $this->valorapuesta = 0;
        $this->porccrit2 = 0;
        $this->porccrit3 = 0;
        $this->porccrit4 = 0;
        $this->porccrit5 = 0;
        $this->porccrit6 = 0;
        $this->porccrit7 = 0;
        $this->porccrit8 = 0;
        $this->porccrit9 = 0;
        $this->estado = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado, $conexion): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id = desordena($resultado[$cq->bd_id]);
            $objeto->idpartido = desordena($resultado[$cq->bd_idpartido]);
            $objeto->partido = Partido::get($objeto->idpartido, $conexion);
            $objeto->idapuestatipo = desordena($resultado[$cq->bd_idapuestatipo]);
            $objeto->apuestatipo = ApuestaTipo::get($objeto->idapuestatipo, $conexion);
            $objeto->valorapuesta = $resultado[$cq->bd_valorapuesta];
            $objeto->porccrit1 = $resultado[$cq->bd_porccrit1];
            $objeto->porccrit2 = $resultado[$cq->bd_porccrit2];
            $objeto->porccrit3 = $resultado[$cq->bd_porccrit3];
            $objeto->porccrit4 = $resultado[$cq->bd_porccrit4];
            $objeto->porccrit5 = $resultado[$cq->bd_porccrit5];
            $objeto->porccrit6 = $resultado[$cq->bd_porccrit6];
            $objeto->porccrit7 = $resultado[$cq->bd_porccrit7];
            $objeto->porccrit8 = $resultado[$cq->bd_porccrit8];
            $objeto->porccrit9 = $resultado[$cq->bd_porccrit9];
            $objeto->estado = $resultado[$cq->bd_estado];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado, $conexion);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getList($param, $conexion): array
    {
        try {
            $idapuestatipo1 = $param['idapuestatipo1'];
            $idapuestatipo2 = $param['idapuestatipo2'];

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
            $query .= "  AND $cqa.$cq->bd_idapuestatipo IN (:idapuestatipo1, :idapuestatipo2) ";
            $query .= "ORDER BY ";
            $query .= "  $cqa.$cq->bd_porccrit5 DESC ";
            $query .= "  ,$cqa.$cq->bd_porccrit9 DESC ";
            $query .= "  ,$cqa.$cq->bd_porccrit2 DESC ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 1);
            $statement->bindValue(":idapuestatipo1", ordena($idapuestatipo1));
            $statement->bindValue(":idapuestatipo2", ordena($idapuestatipo2));
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado, $conexion);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion): void
    {
        try {
            $this->validateData();			

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_idpartido ";
            $query .= "  ,$cq->bd_idapuestatipo ";
            $query .= "  ,$cq->bd_valorapuesta ";
            $query .= "  ,$cq->bd_porccrit1 ";
            $query .= "  ,$cq->bd_porccrit2 ";
            $query .= "  ,$cq->bd_porccrit3 ";
            $query .= "  ,$cq->bd_porccrit4 ";
            $query .= "  ,$cq->bd_porccrit5 ";
            $query .= "  ,$cq->bd_porccrit6 ";
            $query .= "  ,$cq->bd_porccrit7 ";
            $query .= "  ,$cq->bd_porccrit8 ";
            $query .= "  ,$cq->bd_porccrit9 ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_idpartido ";
            $query .= "  ,:$cq->bd_idapuestatipo ";
            $query .= "  ,:$cq->bd_valorapuesta ";
            $query .= "  ,:$cq->bd_porccrit1 ";
            $query .= "  ,:$cq->bd_porccrit2 ";
            $query .= "  ,:$cq->bd_porccrit3 ";
            $query .= "  ,:$cq->bd_porccrit4 ";
            $query .= "  ,:$cq->bd_porccrit5 ";
            $query .= "  ,:$cq->bd_porccrit6 ";
            $query .= "  ,:$cq->bd_porccrit7 ";
            $query .= "  ,:$cq->bd_porccrit8 ";
            $query .= "  ,:$cq->bd_porccrit9 ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idpartido", ordena($this->idpartido));
            $statement->bindValue(":$cq->bd_idapuestatipo", ordena($this->idapuestatipo));
            $statement->bindValue(":$cq->bd_valorapuesta", $this->valorapuesta);
            $statement->bindValue(":$cq->bd_porccrit1", $this->porccrit1);
            $statement->bindValue(":$cq->bd_porccrit2", $this->porccrit2);
            $statement->bindValue(":$cq->bd_porccrit3", $this->porccrit3);
            $statement->bindValue(":$cq->bd_porccrit4", $this->porccrit4);
            $statement->bindValue(":$cq->bd_porccrit5", $this->porccrit5);
            $statement->bindValue(":$cq->bd_porccrit6", $this->porccrit6);
            $statement->bindValue(":$cq->bd_porccrit7", $this->porccrit7);
            $statement->bindValue(":$cq->bd_porccrit8", $this->porccrit8);
            $statement->bindValue(":$cq->bd_porccrit9", $this->porccrit9);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function addComparar($param, $conexion): void
    {
        try {
            $idpartido = $param['idpartido'];
            $idtipoapuesta = $param['idtipoapuesta'];
            $valorapuesta = $param['valorapuesta'];
            $resultados = PartidoApuesta::getProbabilidades($param, $conexion);

            $numeropartidos = $resultados['numeropartidos'];
            $probabilidades_selected = $resultados['probabilidades_selected'];
            
            $newpartidoapuesta = new PartidoApuestaComparar();
            $newpartidoapuesta->idpartido = $idpartido;
            $newpartidoapuesta->idapuestatipo = $idtipoapuesta;
            $newpartidoapuesta->valorapuesta = $valorapuesta;
            $newpartidoapuesta->porccrit1 = $probabilidades_selected['porc_cumple_todos_homevsaway'];
            $newpartidoapuesta->porccrit2 = $probabilidades_selected['porc_cumple_homeathomevsaway'];
            $newpartidoapuesta->porccrit3 = $probabilidades_selected['porc_cumple_homeatawayvsaway'];
            $newpartidoapuesta->porccrit4 = $probabilidades_selected['porc_cumple_homevsall'];
            $newpartidoapuesta->porccrit5 = $probabilidades_selected['porc_cumple_homeathomevsall'];
            $newpartidoapuesta->porccrit6 = $probabilidades_selected['porc_cumple_homeatawayvsall'];
            $newpartidoapuesta->porccrit7 = $probabilidades_selected['porc_cumple_awayvsall'];
            $newpartidoapuesta->porccrit8 = $probabilidades_selected['porc_cumple_awayathomevsall'];
            $newpartidoapuesta->porccrit9 = $probabilidades_selected['porc_cumple_awayatawayvsall'];
            $newpartidoapuesta->add($conexion);  

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function deleteAll($conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_estado = 1 ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validateData(): void
    {
        try {
            

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>