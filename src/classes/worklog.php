<?php

class Worklog {
	public $id_worklog;
	public $datedone;
	public $task;
	public $requestedby;
	public $site;
	public $state;

	public static function get_list($conexion) {
		try {
			$statement = $conexion->prepare(
				"SELECT * FROM work_logs WHERE state = 1 ORDER BY datedone DESC, id_worklog DESC"
			);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$worklogs = array();
	
				foreach ($resultados as $resultado) {
					$worklog = new Worklog;
					$worklog->id_worklog = $resultado['id_worklog'];
					$worklog->datedone = $resultado['datedone'];
					$worklog->task = $resultado['task'];
					$worklog->requestedby = $resultado['requestedby'];
					$worklog->site = $resultado['site'];					
					
					array_push($worklogs, $worklog);
				}
	
				return $worklogs;
			}
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	function add($conexion) {
		try {
			$statement = $conexion->prepare(
				'INSERT INTO work_logs (datedone, task, requestedby, site)
				 VALUES (:datedone, :task, :requestedby, :site)'
			);
			$statement->execute(array(
				':datedone' => $this->datedone,
				':task' => $this->task,
				':requestedby' => $this->requestedby,
				':site' => $this->site,				
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}
	
}

?>