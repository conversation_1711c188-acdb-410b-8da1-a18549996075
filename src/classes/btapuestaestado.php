<?php


class BtApuestaEstado
{
    public string $id;
    public string $nombre;
    public string $color_bg;
    public int $estado;
    public string $bd_table = 'bt_apuestas_estados';
    public string $bd_alias = 'btapest';
    public string $bd_id = 'id';
    public string $bd_nombre = 'nombre';
    public string $bd_color_bg = 'color_bg';
    public string $bd_estado = 'estado';
    const ID_PENDIENTE = 1;
    const ID_CERRADO   = 2;
    const ID_PUBLICADO = 3;

    function __construct()
    {
        $this->id = '';
        $this->nombre = '';
        $this->color_bg = '';
        $this->estado = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id = desordena($resultado[$cq->bd_id]);
            $objeto->nombre = $resultado[$cq->bd_nombre];            
            $objeto->color_bg = $resultado[$cq->bd_color_bg];            
            $objeto->estado = $resultado[$cq->bd_estado];            

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list($paramref, $conexion): array
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = 1 ";

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>