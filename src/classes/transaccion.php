<?php

require_once __ROOT__ . '/src/classes/transaccioncategoria.php';
require_once __ROOT__ . '/src/classes/categoriatransaccion.php';
require_once __ROOT__ . '/src/classes/budget.php';

class Transaccion
{
	public string  $id;
	public float   $valor;
	public string  $fecha;
	public string  $tipo;
	public string  $nota;
	public Budget  $budget;
	public int     $estado;
	public string  $categoria;
	public array   $categorias;
	public string  $categoriasstring;
	public float   $porcentaje;
	public string  $colorchart;
	private string $bd_table             = 'transacciones';
	private string $bd_alias             = 'tra';
	private string $bd_id                = 'id_transaccion';
	private string $bd_valor             = 'valor';
	private string $bd_fecha             = 'fecha';
	private string $bd_tipo              = 'tipo'; // ingreso o egreso
	private string $bd_nota              = 'nota';
	private string $bd_idbudget          = 'id_budget';
	private string $bd_f_nombrebudget    = 'nombre_budget';
	private string $bd_estado            = 'estado';
	private string $bd_f_nombrecategoria = 'nombre_categoria';
	private string $bd_f_colorcategoria  = 'color_categoria';
	
	
	function __construct()
	{
		$this->id                = '';
		$this->valor             = 0;
		$this->fecha             = '';
		$this->tipo              = '';
		$this->nota              = '';
		$this->budget            = new Budget();
		$this->budget->id_budget = '';
		$this->estado            = 0;
		$this->categorias        = array();
		$this->porcentaje        = 0;
		$this->colorchart        = '';
	}
	
	/**
	 * @param $resultado
	 * @param $conexion
	 *
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado, $conexion): self
	{
		try {
			$cq = new self;
			
			$objeto                    = new self;
			$objeto->id                = desordena($resultado[$cq->bd_id]);
			$objeto->valor             = $resultado[$cq->bd_valor];
			$objeto->fecha             = $resultado[$cq->bd_fecha];
			$objeto->tipo              = $resultado[$cq->bd_tipo];
			$objeto->nota              = $resultado[$cq->bd_nota];
			$objeto->budget            = new Budget();
			$objeto->budget->id_budget = desordena($resultado[$cq->bd_idbudget]);
			
			if (isset($resultado[$cq->bd_f_nombrebudget])) {
				$objeto->budget->canal = $resultado[$cq->bd_f_nombrebudget];
			}
			
			$objeto->estado = $resultado[$cq->bd_estado];
			
			$objeto->getCategorias($conexion);
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @param $resultado
	 *
	 * @return self
	 * @throws Exception
	 */
	public static function constructGrouped($resultado): self
	{
		try {
			$cq = new self;
			
			$objeto = new self;
			
			if (isset($resultado[$cq->bd_f_nombrecategoria])) {
				$objeto->categoria = $resultado[$cq->bd_f_nombrecategoria];
			}
			if (isset($resultado[$cq->bd_f_colorcategoria])) {
				$objeto->colorchart = $resultado[$cq->bd_f_colorcategoria];
			}
			if (isset($resultado[$cq->bd_tipo])) {
				$objeto->tipo = $resultado[$cq->bd_tipo];
			}
			
			$objeto->valor = $resultado[$cq->bd_valor];
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, $conexion): self
	{
		try {
			$cq    = new self;
			$cqa   = $cq->bd_alias;
			$cq_b  = new Budget();
			$cq_ba = $cq_b->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "  ,$cq_ba.$cq_b->bd_canal $cq->bd_f_nombrebudget ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "INNER JOIN $cq_b->bd_table $cq_ba ON ($cq_ba.$cq_b->bd_id = $cqa.$cq->bd_idbudget) ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado, $conexion);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public function getCategorias($conexion): void
	{
		try {
			$categorias             = TransaccionCategoria::getList($this->id, $conexion);
			$this->categoriasstring = '';
			
			/** @var TransaccionCategoria[] $categorias */
			foreach ($categorias as $categoria) {
				if (empty($this->categoriasstring)) {
					$this->categoriasstring .= $categoria->categoriatransaccion->nombre;
				} else {
					$this->categoriasstring .= ' / ' . $categoria->categoriatransaccion->nombre;
				}
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getList($parametros, $conexion): array
	{
		try {		
			$preview = $parametros['preview'] ?? 0;
			$nota_filter = $parametros['nota'] ?? '';

			$query = "SELECT ";
			$query .= "  tra.* ";
			$query .= "  ,bud.canal AS nombre_budget ";
			$query .= "FROM transacciones tra ";
			$query .= "INNER JOIN budgets bud ON (tra.id_budget = bud.id_budget) ";
			
			if (!empty($parametros['categoria'])) {
				$query .= "INNER JOIN transacciones_categorias tracat ON (tracat.id_transaccion = tra.id_transaccion) ";
			}
			
			$query .= "WHERE ";
			$query .= "  tra.estado = :estado ";
			
			if (!empty($parametros['categoria'])) {
				$query .= "AND tracat.id_categoria_transaccion = :id_categoria_transaccion ";
			}
			
			if (empty($parametros['mes'])) {
				$query .= "AND tra.fecha >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY) ";
			} else {
				$currentYear = date('Y');
				$previousYear = $currentYear - 1;
				
				$query .= "AND (";
				$query .= "(DATE_FORMAT(tra.fecha, '%Y') = :current_year AND DATE_FORMAT(tra.fecha, '%m') = :mes) ";
				$query .= "OR (DATE_FORMAT(tra.fecha, '%Y') = :previous_year AND DATE_FORMAT(tra.fecha, '%m') = :mes AND NOT EXISTS (";
				$query .= "    SELECT 1 FROM transacciones t WHERE DATE_FORMAT(t.fecha, '%Y') = :current_year AND DATE_FORMAT(t.fecha, '%m') = :mes AND t.estado = 1";
				$query .= "))";
				$query .= ") ";
			}

			if (!empty($nota_filter)) {
				$query .= "AND tra.nota LIKE :nota_filter ";
			}
			
			$query .= "GROUP BY ";
			$query .= "  tra.id_transaccion ";
			$query .= "ORDER BY ";
			$query .= "  tra.fecha DESC ";
			$query .= "  ,tra.id_transaccion DESC ";

			if($preview == 1){
				$query .= "LIMIT 10";
			}
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":estado", 1);
			
			if (!empty($parametros['categoria'])) {
				$statement->bindValue(":id_categoria_transaccion", ordena($parametros['categoria']));
			}
			
			if (!empty($parametros['mes'])) {
				$currentYear = date('Y');
				$previousYear = $currentYear - 1;
				
				$statement->bindValue(":mes", $parametros['mes']);
				$statement->bindValue(":current_year", $currentYear);
				$statement->bindValue(":previous_year", $previousYear);
			}

			if (!empty($nota_filter)) {
				$statement->bindValue(":nota_filter", "%" . $nota_filter . "%");
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado, $conexion);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getListGrouped($conexion): array
	{
		try {
			$cq     = new self;
			$cqa    = $cq->bd_alias;
			$cq_tc  = new TransaccionCategoria();
			$cq_tca = $cq_tc->bd_alias;
			$cq_ct  = new CategoriaTransaccion();
			$cq_cta = $cq_ct->bd_alias;
			
			$mesactual  = getMesActual();
			$yearactual = getYearActual();
			
			$query = "SELECT ";
			$query .= "  $cq_cta.$cq_ct->bd_nombre $cq->bd_f_nombrecategoria ";
			$query .= "  ,SUM(ABS($cqa.$cq->bd_valor)) $cq->bd_valor ";
			$query .= "  ,$cq_cta.$cq_ct->bd_color $cq->bd_f_colorcategoria ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "INNER JOIN $cq_tc->bd_table $cq_tca ON ($cqa.$cq->bd_id = $cq_tca.$cq_tc->bd_idtransaccion) ";
			$query .= "INNER JOIN $cq_ct->bd_table $cq_cta ON ($cq_tca.$cq_tc->bd_idcategoriatransaccion = $cq_cta.$cq_ct->bd_id) ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
			$query .= "  AND DATE_FORMAT($cq->bd_fecha, '%Y') = :yearactual ";
			$query .= "  AND DATE_FORMAT($cq->bd_fecha, '%m') = :mesactual ";
			$query .= "GROUP BY ";
			$query .= "  $cq_tca.$cq_tc->bd_idcategoriatransaccion ";
			$query .= "ORDER BY ";
			$query .= "  $cq_cta.$cq_ct->bd_nombre ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 1);
			$statement->bindValue(":yearactual", $yearactual);
			$statement->bindValue(":mesactual", $mesactual);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::constructGrouped($resultado);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getListGroupedTipo($conexion): array
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$mesactual  = getMesActual();
			$yearactual = getYearActual();
			
			$query = "SELECT ";
			$query .= "  $cqa.$cq->bd_tipo ";
			$query .= "  ,SUM(ABS($cqa.$cq->bd_valor)) $cq->bd_valor ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
			$query .= "  AND DATE_FORMAT($cq->bd_fecha, '%Y') = :yearactual ";
			$query .= "  AND DATE_FORMAT($cq->bd_fecha, '%m') = :mesactual ";
			$query .= "GROUP BY ";
			$query .= "  $cqa.$cq->bd_tipo ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 1);
			$statement->bindValue(":yearactual", $yearactual);
			$statement->bindValue(":mesactual", $mesactual);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::constructGrouped($resultado);
				}
				
				return self::calculateRatioByTipo($listado);
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function getListCategorias($categorias): void
	{
		try {
			$chosecategorias = array();
			
			foreach ($categorias as $categoria) {
				$chosecategoria                           = new TransaccionCategoria();
				$chosecategoria->categoriatransaccion     = new CategoriaTransaccion();
				$chosecategoria->categoriatransaccion->id = $categoria;
				
				$chosecategorias[] = $chosecategoria;
			}
			
			$this->categorias = $chosecategorias;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function add($conexion): void
	{
		try {
			$this->addItem($conexion);
			TransaccionCategoria::addMultiple($this->id, $this->categorias, $conexion);
			Budget::modifyValor($this, 0, $conexion);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function addItem($conexion): void
	{
		try {
			validar_textovacio($this->budget->id_budget, 'Debe especificar el valor');
			validar_textovacio($this->valor, 'Debe especificar el valor');
			validar_textovacio($this->fecha, 'Debe especificar la fecha');
			validar_array($this->categorias, 'Debe especificar las categorias');
			
			$cq = new self;
			
			$this->valor = format_numberclean($this->valor);
			$this->asignar_valor();
			
			$query = "INSERT INTO $cq->bd_table (";
			$query .= "  $cq->bd_valor ";
			$query .= "  ,$cq->bd_fecha ";
			$query .= "  ,$cq->bd_tipo ";
			$query .= "  ,$cq->bd_nota ";
			$query .= "  ,$cq->bd_idbudget ";
			$query .= ") VALUES (";
			$query .= "  :$cq->bd_valor ";
			$query .= "  ,:$cq->bd_fecha ";
			$query .= "  ,:$cq->bd_tipo ";
			$query .= "  ,:$cq->bd_nota ";
			$query .= "  ,:$cq->bd_idbudget ";
			$query .= ") ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_valor", $this->valor);
			$statement->bindValue(":$cq->bd_fecha", $this->fecha);
			$statement->bindValue(":$cq->bd_tipo", $this->tipo);
			$statement->bindValue(":$cq->bd_nota", $this->nota);
			$statement->bindValue(":$cq->bd_idbudget", ordena($this->budget->id_budget));
			$statement->execute();
			
			$this->id = desordena($conexion->lastInsertId());
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modifyGeneral($conexion): void
	{
		try {
			$oldtransaccion = self::get($this->id, $conexion);
			
			Budget::modifyValor($oldtransaccion, 1, $conexion);
			$this->modifyItemGeneral($conexion);
			Budget::modifyValor($this, 0, $conexion);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modifyCategorias($conexion): void
	{
		try {
			TransaccionCategoria::addMultiple($this->id, $this->categorias, $conexion);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function modifyItemGeneral($conexion): void
	{
		try {
			$cq = new self;
			
			$this->valor = format_numberclean($this->valor);
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_valor = :$cq->bd_valor ";
			$query .= "  ,$cq->bd_fecha = :$cq->bd_fecha ";
			$query .= "  ,$cq->bd_tipo = :$cq->bd_tipo ";
			$query .= "  ,$cq->bd_nota = :$cq->bd_nota ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_valor", $this->valor);
			$statement->bindValue(":$cq->bd_fecha", $this->fecha);
			$statement->bindValue(":$cq->bd_tipo", $this->tipo);
			$statement->bindValue(":$cq->bd_nota", $this->nota);
			$statement->bindValue(":$cq->bd_id", ordena($this->id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function delete($id, $conexion): void
	{
		try {
			$deltransaccion = self::get($id, $conexion);
			
			self::deleteItem($id, $conexion);
			Budget::modifyValor($deltransaccion, 1, $conexion);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public function modifyPartial(PDO $conexion): void
	{
		try {
			validar_textovacio($this->id, 'ID de transacción es requerido.');
			validar_textovacio($this->fecha, 'Debe especificar la fecha.');
			// Nota can be empty, so no specific validation here unless business rule changes.
			
			$cq = new self;
			
			$query = "UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_fecha = :$cq->bd_fecha, ";
			$query .= "  $cq->bd_nota = :$cq->bd_nota ";
			$query .= "WHERE $cq->bd_id = :$cq->bd_id";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_fecha", $this->fecha);
			$statement->bindValue(":$cq->bd_nota", $this->nota);
			$statement->bindValue(":$cq->bd_id", ordena($this->id));
			$statement->execute();
			
			if ($statement->rowCount() == 0) {
				// Optional: throw an exception if no rows were updated, could mean ID not found or data was the same
				// throw new Exception("No se actualizó ninguna fila, verifique el ID de la transacción.");
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	/**
	 * @throws Exception
	 */
	private static function deleteItem($id, $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_estado = :$cq->bd_estado ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 0);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private static function calculateRatioByTipo($transaccionesgrouped): array
	{
		try {
			$total = 0;
			
			/** @var self[] $transaccionesgrouped */
			foreach ($transaccionesgrouped as $transacciongrouped) {
				$total += $transacciongrouped->valor;
			}
			foreach ($transaccionesgrouped as $transacciongrouped) {
				$transacciongrouped->porcentaje = round(($transacciongrouped->valor * 100) / $total);
			}
			
			return $transaccionesgrouped;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getUniqueNotasLastYear(PDO $conexion): array
	{
		try {
			$query = <<<SQL
			SELECT DISTINCT nota
			FROM transacciones
			WHERE fecha >= DATE_SUB(CURDATE(), INTERVAL 365 DAY)
			AND nota IS NOT NULL AND nota != ''
			ORDER BY nota ASC
			SQL;
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_COLUMN);
			
			return $resultados ?: array();
			
		} catch (Exception $e) {
			throw new Exception("Error fetching unique notes: " . $e->getMessage());
		}
	}

	/**
	 * @throws Exception
	 */
	private function asignar_valor(): void
	{
		try {
			if ($this->tipo == 'EGRESO') {
				$this->valor = $this->valor * -1;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>
