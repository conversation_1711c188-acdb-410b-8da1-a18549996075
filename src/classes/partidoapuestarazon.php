<?php

require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/razonperdida.php';

class PartidoApuestaRazon
{
    public string $id;
    public string $id_partido_apuesta;
    public string $nombre_razon_perdida;
    private string $bd_table = 'partidos_apuestas_razones';
    private string $bd_alias = 'parapraz';
    private string $bd_id = 'id_partido_apuesta_razon';
    private string $bd_id_partido_apuesta = 'id_partido_apuesta';
    private string $bd_nombre_razon_perdida = 'nombre_razon_perdida';

    function __construct()
    {
        $this->id = '';
        $this->id_partido_apuesta = '';
        $this->nombre_razon_perdida = '';
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id                   = desordena($resultado[$cq->bd_id]);
            $objeto->id_partido_apuesta   = desordena($resultado[$cq->bd_id_partido_apuesta]);
            $objeto->nombre_razon_perdida = $resultado[$cq->bd_nombre_razon_perdida];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_count($paramref, PDO $conexion): int
    {
        try {
            $fecharange   = (isset($paramref['fecharange'])) ? $paramref['fecharange'] : "";
            $solo_ganado  = (isset($paramref['solo_ganado'])) ? $paramref['solo_ganado'] : 0;
            $solo_perdido = (isset($paramref['solo_perdido'])) ? $paramref['solo_perdido'] : 0;
            $nombre_razon = (isset($paramref['nombre_razon'])) ? $paramref['nombre_razon'] : '';

            $fecharange = explode(" - ", $fecharange);

            $cq_partido_apuesta_razon  = new self;
            $cqa_partido_apuesta_razon = $cq_partido_apuesta_razon->bd_alias;
            $cq_partido_apuesta        = new PartidoApuesta();
            $cqa_partido_apuesta       = $cq_partido_apuesta->bd_alias;
            $cq_partido                = new Partido();
            $cqa_partido               = $cq_partido->bd_alias;

            #region region query
            $query = "SELECT ";
            $query .= "  COUNT($cqa_partido_apuesta_razon.$cq_partido_apuesta_razon->bd_id) ";
            $query .= "FROM $cq_partido_apuesta_razon->bd_table $cqa_partido_apuesta_razon ";
            $query .= "INNER JOIN $cq_partido_apuesta->bd_table $cqa_partido_apuesta ";
            $query .= "  ON ($cqa_partido_apuesta_razon.$cq_partido_apuesta_razon->bd_id_partido_apuesta = $cqa_partido_apuesta.$cq_partido_apuesta->bd_id) ";
            $query .= "INNER JOIN $cq_partido->bd_table $cqa_partido ";
            $query .= "  ON ($cqa_partido_apuesta.$cq_partido_apuesta->bd_idpartido = $cqa_partido.$cq_partido->bd_id) ";

            #region region where
            $query .= "WHERE ";
            $query .= "  $cqa_partido.$cq_partido->bd_estado = 1 ";
            $query .= "  AND $cqa_partido_apuesta.$cq_partido_apuesta->bd_estado = 1 ";

            if (!empty($fecharange)) {
                $query .= "AND $cqa_partido.$cq_partido->bd_fecha >= :fecha1 ";
                $query .= "AND $cqa_partido.$cq_partido->bd_fecha <= :fecha2 ";
            }
            if ($solo_ganado == 1) {
                $query .= "AND $cqa_partido_apuesta.$cq_partido_apuesta->bd_profit > 0 ";
            }
            if ($solo_perdido == 1) {
                $query .= "AND $cqa_partido_apuesta.$cq_partido_apuesta->bd_profit < 0 ";
            }
            if(!empty($nombre_razon)){
                $query .= "AND $cqa_partido_apuesta_razon.$cq_partido_apuesta_razon->bd_nombre_razon_perdida = :$cq_partido_apuesta_razon->bd_nombre_razon_perdida ";
            }
            #endregion where
            #endregion query

            $statement = $conexion->prepare($query);

            if (!empty($fecharange)){
                $statement->bindValue(":fecha1", $fecharange[0]);
                $statement->bindValue(":fecha2", $fecharange[1]);
            }
            if(!empty($nombre_razon)){
                $statement->bindValue(":$cq_partido_apuesta_razon->bd_nombre_razon_perdida", $nombre_razon);
            }

            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return $resultado[0];

            } else {
                return 0;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list(array $paramref,PDO $conexion): array
    {
        try {
            $fecharange = (isset($paramref['fecharange'])) ? $paramref['fecharange'] : "";
            $group_razon = (isset($paramref['group_razon'])) ? $paramref['group_razon'] : "";

            $fecharange = explode(" - ", $fecharange);

            $cq_partido_apuesta_razon  = new self;
            $cqa_partido_apuesta_razon = $cq_partido_apuesta_razon->bd_alias;
            $cq_partido_apuesta        = new PartidoApuesta();
            $cqa_partido_apuesta       = $cq_partido_apuesta->bd_alias;
            $cq_partido                = new Partido();
            $cqa_partido               = $cq_partido->bd_alias;

            #region region query
            $query = "SELECT ";
            $query .= "  $cqa_partido_apuesta_razon.* ";
            $query .= "FROM $cq_partido_apuesta_razon->bd_table $cqa_partido_apuesta_razon ";
            $query .= "INNER JOIN $cq_partido_apuesta->bd_table $cqa_partido_apuesta ";
            $query .= "  ON ($cqa_partido_apuesta_razon.$cq_partido_apuesta_razon->bd_id_partido_apuesta = $cqa_partido_apuesta.$cq_partido_apuesta->bd_id) ";
            $query .= "INNER JOIN $cq_partido->bd_table $cqa_partido ";
            $query .= "  ON ($cqa_partido_apuesta.$cq_partido_apuesta->bd_idpartido = $cqa_partido.$cq_partido->bd_id) ";

            #region region where
            $query .= "WHERE ";
            $query .= "  $cqa_partido.$cq_partido->bd_estado = 1 ";
            $query .= "  AND $cqa_partido_apuesta.$cq_partido_apuesta->bd_estado = 1 ";

            if (!empty($fecharange)) {
                $query .= "  AND $cqa_partido.$cq_partido->bd_fecha >= :fecha1 ";
                $query .= "  AND $cqa_partido.$cq_partido->bd_fecha <= :fecha2 ";
            }
            #endregion where

            #region region group by and order by
            if ($group_razon == 1) {
                $query .= "GROUP BY ";
                $query .= "  $cqa_partido_apuesta_razon.$cq_partido_apuesta_razon->bd_nombre_razon_perdida ";
            }

            $query .= "ORDER BY ";
            $query .= "  $cqa_partido_apuesta_razon.$cq_partido_apuesta_razon->bd_nombre_razon_perdida ";
            #endregion group by and order by
            #endregion query

            $statement = $conexion->prepare($query);

            #region region bindvalue
            $statement->bindValue(":fecha1", $fecharange[0]);
            $statement->bindValue(":fecha2", $fecharange[1]);
            #endregion bindvalue

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion): void
    {
        try {
            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_id_partido_apuesta ";
            $query .= "  ,$cq->bd_nombre_razon_perdida ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_id_partido_apuesta ";
            $query .= "  ,:$cq->bd_nombre_razon_perdida ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id_partido_apuesta", ordena($this->id_partido_apuesta));
            $statement->bindValue(":$cq->bd_nombre_razon_perdida", $this->nombre_razon_perdida);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function add_multiple($id_partido_apuesta, array $razones, PDO $conexion): void
    {
        try {
            self::delete($id_partido_apuesta, $conexion);

            for ($i = 0; $i < count($razones); $i++){
                $new_razon = new self;
                $new_razon->id_partido_apuesta   = $id_partido_apuesta;
                $new_razon->nombre_razon_perdida = $razones[$i]['nombre'];
                $new_razon->add($conexion);
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id_partido_apuesta, PDO $conexion): void
    {
        try {
            $cq = new self;

            $query = " DELETE FROM $cq->bd_table ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id_partido_apuesta = :$cq->bd_id_partido_apuesta ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id_partido_apuesta", ordena($id_partido_apuesta));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>