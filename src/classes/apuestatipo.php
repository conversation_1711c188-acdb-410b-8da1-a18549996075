<?php


class ApuestaTipo
{
    public string $id;
    public string $nombre;
    public int $iscombinada;
    public float $val_penalidad;
    public float $min_valor_apuesta;
    public float $max_valor_apuesta;
    public float $def_valor_apuesta;
    public int $n_puesto;
    public int $revision_activa;
    public float $perc_penalidad;
    public int $estado;
    public array $reporte_winratioapuestatipo;
    public string $bd_table = 'apuestas_tipos';
    public string $bd_alias = 'apti';
    public string $bd_id = 'id_apuesta_tipo';
    public string $bd_nombre = 'nombre';
    public string $bd_iscombinada = 'is_combinada';
    public string $bd_val_penalidad = 'val_penalidad';
    public string $bd_min_valor_apuesta = 'min_valor_apuesta';
    public string $bd_max_valor_apuesta = 'max_valor_apuesta';
    public string $bd_def_valor_apuesta = 'def_valor_apuesta';
    public string $bd_n_puesto = 'n_puesto';
    public string $bd_revision_activa = 'revision_activa';
    public string $bd_perc_penalidad = 'perc_penalidad';
    public string $bd_estado = 'estado';
    const ID_MASXTOTALCORNERS = 2;
    const ID_TOTAL_CORNERS_MASDE = 2;
    const ID_MENOSXTOTALCORNERS = 3;
    const ID_TOTAL_CORNERS_MENOSDE = 3;
    const ID_MASXCORNERSHOME = 4;
    const ID_CORNERS_HOME_MASDE = 4;
    const ID_MENOSXCORNERSHOME = 5;
    const ID_CORNERS_HOME_MENOSDE = 5;
    const ID_MASXCORNERSAWAY = 6;
    const ID_CORNERS_AWAY_MASDE = 6;
    const ID_MENOSXCORNERSAWAY = 7;
    const ID_CORNERS_AWAY_MENOSDE = 7;
    const ID_MASCORNERSHOME = 8;
    const ID_MASCORNERSAWAY = 9;
    const ID_COMBINADA2 = 10;
    const ID_COMBINADA3 = 11;
    const ID_TOTAL_GOLES_MASDE = 28;
    const ID_HOME_WINS = 29;
    const ID_BTTS_YES = 32;
    const ID_BTTS_NO = 36;
    const ID_AWAY_WINS = 33;
    const ID_TOTAL_GOLES_MENOSDE = 37;
    const ID_GOLES_HOME_MASDE = 43;
    const ID_GOLES_HOME_MENOSDE = 44;
    const ID_GOLES_AWAY_MASDE = 45;
    const ID_GOLES_AWAY_MENOSDE = 46;

    function __construct()
    {
        $this->id = '';
        $this->nombre = '';
        $this->iscombinada = 0;
        $this->val_penalidad = 0;
        $this->min_valor_apuesta = 0;
        $this->max_valor_apuesta = 0;
        $this->def_valor_apuesta = 0;
        $this->n_puesto = 0;
        $this->revision_activa = 0;
        $this->perc_penalidad = 0;
        $this->estado = 0;
        $this->reporte_winratioapuestatipo = array();
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id                = desordena($resultado[$cq->bd_id]);
            $objeto->nombre            = $resultado[$cq->bd_nombre];
            $objeto->iscombinada       = $resultado[$cq->bd_iscombinada];
            $objeto->val_penalidad     = $resultado[$cq->bd_val_penalidad];
            $objeto->min_valor_apuesta = $resultado[$cq->bd_min_valor_apuesta];
            $objeto->max_valor_apuesta = $resultado[$cq->bd_max_valor_apuesta];
            $objeto->def_valor_apuesta = $resultado[$cq->bd_def_valor_apuesta];
            $objeto->n_puesto          = $resultado[$cq->bd_n_puesto];
            $objeto->revision_activa   = $resultado[$cq->bd_revision_activa];
            $objeto->perc_penalidad    = $resultado[$cq->bd_perc_penalidad];
            $objeto->estado            = $resultado[$cq->bd_estado];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_by_n_puesto($n_puesto, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_n_puesto = :$cq->bd_n_puesto ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_n_puesto", $n_puesto);
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getIdByNombre($nombre, $conexion): string
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_nombre = :$cq->bd_nombre ";
            $query .= "  AND $cqa.$cq->bd_estado = :$cq->bd_estado ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 1);
            $statement->bindValue(":$cq->bd_nombre", $nombre);
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return desordena($resultado[$cq->bd_id]);

            } else {
                return '';
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getList(array $paramref, $conexion): array
    {
        try {
            $solo_revision_activa = (isset($paramref['solo_revision_activa'])) ? $paramref['solo_revision_activa'] : 0;
            $orderby_n_puesto     = (isset($paramref['orderby_n_puesto'])) ? $paramref['orderby_n_puesto'] : 0;

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";

            if($solo_revision_activa == 1){
                $query .= "AND $cqa.$cq->bd_revision_activa = 1 ";
            }

            #region region order by
            $query .= "ORDER BY  ";

            if ($orderby_n_puesto == 1) {
                $query .= "$cq->bd_n_puesto ASC ";
            } else {
                $query .= "$cq->bd_id ";
            }
            #endregion order by

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 1);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getListSoloIsCombinada($conexion): array
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
            $query .= "  AND $cqa.$cq->bd_iscombinada = :$cq->bd_iscombinada ";
            $query .= "ORDER BY  ";
            $query .= "  $cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 1);
            $statement->bindValue(":$cq->bd_iscombinada", 1);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion): void
    {
        try {
            $this->validateData();			

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_nombre ";
            $query .= "  ,$cq->bd_val_penalidad ";
            $query .= "  ,$cq->bd_perc_penalidad ";
            $query .= "  ,$cq->bd_min_valor_apuesta ";
            $query .= "  ,$cq->bd_max_valor_apuesta ";
            $query .= "  ,$cq->bd_def_valor_apuesta ";
            $query .= "  ,$cq->bd_n_puesto ";
            $query .= "  ,$cq->bd_revision_activa ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_nombre ";
            $query .= "  ,:$cq->bd_val_penalidad ";
            $query .= "  ,:$cq->bd_perc_penalidad ";
            $query .= "  ,:$cq->bd_min_valor_apuesta ";
            $query .= "  ,:$cq->bd_max_valor_apuesta ";
            $query .= "  ,:$cq->bd_def_valor_apuesta ";
            $query .= "  ,:$cq->bd_n_puesto ";
            $query .= "  ,:$cq->bd_revision_activa ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->bindValue(":$cq->bd_val_penalidad", $this->val_penalidad);
            $statement->bindValue(":$cq->bd_perc_penalidad", $this->perc_penalidad);
            $statement->bindValue(":$cq->bd_min_valor_apuesta", $this->min_valor_apuesta);
            $statement->bindValue(":$cq->bd_max_valor_apuesta", $this->max_valor_apuesta);
            $statement->bindValue(":$cq->bd_def_valor_apuesta", $this->def_valor_apuesta);
            $statement->bindValue(":$cq->bd_n_puesto", $this->n_puesto);
            $statement->bindValue(":$cq->bd_revision_activa", $this->revision_activa);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modify($conexion): void
    {
        try {
            $this->validateData();

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_nombre = :$cq->bd_nombre ";
            $query .= "  ,$cq->bd_val_penalidad = :$cq->bd_val_penalidad ";
            $query .= "  ,$cq->bd_perc_penalidad = :$cq->bd_perc_penalidad ";
            $query .= "  ,$cq->bd_min_valor_apuesta = :$cq->bd_min_valor_apuesta ";
            $query .= "  ,$cq->bd_max_valor_apuesta = :$cq->bd_max_valor_apuesta ";
            $query .= "  ,$cq->bd_def_valor_apuesta = :$cq->bd_def_valor_apuesta ";
            $query .= "  ,$cq->bd_n_puesto = :$cq->bd_n_puesto ";
            $query .= "  ,$cq->bd_revision_activa = :$cq->bd_revision_activa ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->bindValue(":$cq->bd_val_penalidad", $this->val_penalidad);
            $statement->bindValue(":$cq->bd_perc_penalidad", $this->perc_penalidad);
            $statement->bindValue(":$cq->bd_min_valor_apuesta", $this->min_valor_apuesta);
            $statement->bindValue(":$cq->bd_max_valor_apuesta", $this->max_valor_apuesta);
            $statement->bindValue(":$cq->bd_def_valor_apuesta", $this->def_valor_apuesta);
            $statement->bindValue(":$cq->bd_n_puesto", $this->n_puesto);
            $statement->bindValue(":$cq->bd_revision_activa", $this->revision_activa);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validateData(): void
    {
        try {
            validar_textovacionotzero($this->nombre, 'Debe especificar el nombre.');

            $this->nombre = strtoupper($this->nombre);

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>