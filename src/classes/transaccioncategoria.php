<?php

require_once __ROOT__ . '/src/classes/transaccion.php';
require_once __ROOT__ . '/src/classes/categoriatransaccion.php';

class TransaccionCategoria
{
    public string $id;
    public Transaccion $transaccion;
    public CategoriaTransaccion $categoriatransaccion;
    public string $bd_table = 'transacciones_categorias';
    public string $bd_alias = 'tracat';
    public string $bd_id = 'id_transaccion_categoria';
    public string $bd_idtransaccion = 'id_transaccion';
    public string $bd_idcategoriatransaccion = 'id_categoria_transaccion';
    public string $bd_f_nombrecategoriatransaccion = 'nombre_categoria_transaccion';

    function __construct()
    {
        $this->id = '';
        $this->transaccion = new Transaccion();
        $this->categoriatransaccion = new CategoriaTransaccion();
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id = desordena($resultado[$cq->bd_id]);
            $objeto->transaccion = new Transaccion();
            $objeto->transaccion->id = desordena($resultado[$cq->bd_idtransaccion]);
            $objeto->categoriatransaccion = new CategoriaTransaccion();
            $objeto->categoriatransaccion->id = desordena($resultado[$cq->bd_idcategoriatransaccion]);

            if (isset($resultado[$cq->bd_f_nombrecategoriatransaccion])) {
                $objeto->categoriatransaccion->nombre = $resultado[$cq->bd_f_nombrecategoriatransaccion];
            }

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getList($idtransaccion, $conexion): array
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;
            $cq_ct = new CategoriaTransaccion();
            $cq_cta = $cq_ct->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "  ,$cq_cta.$cq_ct->bd_nombre $cq->bd_f_nombrecategoriatransaccion ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "INNER JOIN $cq_ct->bd_table $cq_cta ON ($cqa.$cq->bd_idcategoriatransaccion = $cq_cta.$cq_ct->bd_id) ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_idtransaccion = :$cq->bd_idtransaccion ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idtransaccion", ordena($idtransaccion));
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion): void
    {
        try {
            $cq = new self;

            $query = self::addQuery();

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idtransaccion", ordena($this->transaccion->id));
            $statement->bindValue(":$cq->bd_idcategoriatransaccion", ordena($this->categoriatransaccion->id));
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function addMultiple($idtransaccion, $categorias, $conexion): void
    {
        try {
            $cq = new self;

            self::deleteFromTransaccion($idtransaccion, $conexion);

            $query = self::addQuery();
            $statement = $conexion->prepare($query);

            /** @var self $categorias */
            foreach ($categorias as $categoria) {
                $statement->bindValue(":$cq->bd_idtransaccion", ordena($idtransaccion));
                $statement->bindValue(":$cq->bd_idcategoriatransaccion", ordena($categoria->categoriatransaccion->id));
                $statement->execute();
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private static function addQuery(): string
    {
        try {
            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_idtransaccion ";
            $query .= "  ,$cq->bd_idcategoriatransaccion ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_idtransaccion ";
            $query .= "  ,:$cq->bd_idcategoriatransaccion ";
            $query .= ") ";

            return $query;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function deleteFromTransaccion($idtransaccion, $conexion): void
    {
        try {
            $cq = new self;

            $query = " DELETE FROM $cq->bd_table ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_idtransaccion = :$cq->bd_idtransaccion ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idtransaccion", ordena($idtransaccion));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " DELETE FROM $cq->bd_table ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>