<?php

require_once __ROOT__ . '/src/classes/tickergrupo.php';
require_once __ROOT__ . '/src/classes/tickersubgrupo.php';

class Ticker
{
	public string $id;
	public string $nombre;
	public TickerGrupo $ticker_grupo;
	public TickerSubGrupo $ticker_subgrupo;
	public string $next_eps;
	public string $next_eps_dias;
	public int $eps_positivo_q1;
	public int $eps_positivo_q2;
	public int $eps_positivo_q3;
	public float $eps_porc_change_q1;
	public float $eps_porc_change_q2;
	public float $eps_porc_change_q3;
	public float $eps_porc_change_avg;
	public float $eps_porc_surprise_q1;
	public float $eps_porc_surprise_q2;
	public float $eps_porc_surprise_q3;
	public float $eps_porc_surprise_avg;
	public float $sales_porc_change_q1;
	public float $sales_porc_change_q2;
	public float $sales_porc_change_q3;
	public float $sales_porc_change_avg;
	public float $sales_porc_surprise_q1;
	public float $sales_porc_surprise_q2;
	public float $sales_porc_surprise_q3;
	public float $sales_porc_surprise_avg;
	public float $rsi;
	public int $sma_1mo_up;
	public int $sma_4mo_up;
	public float $base;
	public float $t1;
	public float $t2;
	public float $t3;
	public int $caruso_bars;
	public int $puntos;
	public string $fecha_actualizado;
	public int $para_comprar;
	public int $en_standby;
	public int $vetado;
	public int $con_posicion;
	public int $estado;
	public string $ultimo_tx;
	public float $porc_ultimo_tx;
	public string $eps_positivos;
	public int $fecha_actualizado_dias;
	public string $bd_table = 'tickers';
	public string $bd_alias = 'tic';
	public string $bd_id = 'id';
	public string $bd_nombre = 'nombre';
	public string $bd_id_ticker_grupo = 'id_ticker_grupo';
	public string $bd_f_nombre_ticker_grupo = 'nom_ticker_grupo';
	public string $bd_id_ticker_subgrupo = 'id_ticker_subgrupo';
	public string $bd_f_nombre_ticker_subgrupo = 'nom_ticker_subgrupo';
	public string $bd_next_eps = 'next_eps';
	public string $bd_eps_positivo_q1 = 'eps_positivo_q1';
	public string $bd_eps_positivo_q2 = 'eps_positivo_q2';
	public string $bd_eps_positivo_q3 = 'eps_positivo_q3';
	public string $bd_eps_porc_change_q1 = 'eps_porc_change_q1';
	public string $bd_eps_porc_change_q2 = 'eps_porc_change_q2';
	public string $bd_eps_porc_change_q3 = 'eps_porc_change_q3';
	public string $bd_eps_porc_change_avg = 'eps_porc_change_avg';
	public string $bd_eps_porc_surprise_q1 = 'eps_porc_surprise_q1';
	public string $bd_eps_porc_surprise_q2 = 'eps_porc_surprise_q2';
	public string $bd_eps_porc_surprise_q3 = 'eps_porc_surprise_q3';
	public string $bd_eps_porc_surprise_avg = 'eps_porc_surprise_avg';
	public string $bd_sales_porc_change_q1 = 'sales_porc_change_q1';
	public string $bd_sales_porc_change_q2 = 'sales_porc_change_q2';
	public string $bd_sales_porc_change_q3 = 'sales_porc_change_q3';
	public string $bd_sales_porc_change_avg = 'sales_porc_change_avg';
	public string $bd_sales_porc_surprise_q1 = 'sales_porc_surprise_q1';
	public string $bd_sales_porc_surprise_q2 = 'sales_porc_surprise_q2';
	public string $bd_sales_porc_surprise_q3 = 'sales_porc_surprise_q3';
	public string $bd_sales_porc_surprise_avg = 'sales_porc_surprise_avg';
	public string $bd_rsi = 'rsi';
	public string $bd_sma_1mo_up = 'sma_1mo_up';
	public string $bd_sma_4mo_up = 'sma_4mo_up';
	public string $bd_base = 'base';
	public string $bd_t1 = 't1';
	public string $bd_t2 = 't2';
	public string $bd_t3 = 't3';
	public string $bd_caruso_bars = 'caruso_bars';
	public string $bd_puntos = 'puntos';
	public string $bd_fecha_actualizado = 'fecha_actualizado';
	public string $bd_para_comprar = 'para_comprar';
	public string $bd_en_standby = 'en_standby';
	public string $bd_vetado = 'vetado';
	public string $bd_con_posicion = 'con_posicion';
	public string $bd_estado = 'estado';
	const MINIMO_ULTIMO_TX = 8;
	
	function __construct()
	{
		$this->id                      = '';
		$this->nombre                  = '';
		$this->ticker_grupo            = new TickerGrupo();
		$this->ticker_subgrupo         = new TickerSubGrupo();
		$this->next_eps                = '';
		$this->next_eps_dias           = '';
		$this->eps_positivo_q1         = 0;
		$this->eps_positivo_q2         = 0;
		$this->eps_positivo_q3         = 0;
		$this->eps_porc_change_q1      = 0;
		$this->eps_porc_change_q2      = 0;
		$this->eps_porc_change_q3      = 0;
		$this->eps_porc_change_avg     = 0;
		$this->eps_porc_surprise_q1    = 0;
		$this->eps_porc_surprise_q2    = 0;
		$this->eps_porc_surprise_q3    = 0;
		$this->eps_porc_surprise_avg   = 0;
		$this->sales_porc_change_q1    = 0;
		$this->sales_porc_change_q2    = 0;
		$this->sales_porc_change_q3    = 0;
		$this->sales_porc_change_avg   = 0;
		$this->sales_porc_surprise_q1  = 0;
		$this->sales_porc_surprise_q2  = 0;
		$this->sales_porc_surprise_q3  = 0;
		$this->sales_porc_surprise_avg = 0;
		$this->rsi                     = 0;
		$this->sma_1mo_up              = 0;
		$this->sma_4mo_up              = 0;
		$this->base                    = 0;
		$this->t1                      = 0;
		$this->t2                      = 0;
		$this->t3                      = 0;
		$this->caruso_bars             = 0;
		$this->puntos                  = 0;
		$this->fecha_actualizado       = '';
		$this->fecha_actualizado_dias  = 0;
		$this->para_comprar            = 0;
		$this->en_standby              = 0;
		$this->vetado                  = 0;
		$this->con_posicion            = 0;
		$this->estado                  = 0;
		$this->ultimo_tx               = '';
		$this->porc_ultimo_tx          = 0;
		$this->eps_positivos           = '';
	}
	
	/**
	 * @param $resultado
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$cq = new self;
			
			$objeto                          = new self;
			$objeto->id                      = desordena($resultado[$cq->bd_id]);
			$objeto->nombre                  = $resultado[$cq->bd_nombre];
			$objeto->ticker_grupo->id        = desordena($resultado[$cq->bd_id_ticker_grupo]);
			$objeto->ticker_grupo->nombre    = (isset($resultado[$cq->bd_f_nombre_ticker_grupo])) ? $resultado[$cq->bd_f_nombre_ticker_grupo] : "";
			$objeto->ticker_subgrupo->id     = desordena($resultado[$cq->bd_id_ticker_subgrupo]);
			$objeto->ticker_subgrupo->nombre = (isset($resultado[$cq->bd_f_nombre_ticker_subgrupo])) ? $resultado[$cq->bd_f_nombre_ticker_subgrupo] : "";
			$objeto->next_eps                = $resultado[$cq->bd_next_eps];
			$objeto->next_eps_dias           = getDateDiffDays(create_date(), $objeto->next_eps);
			$objeto->eps_positivo_q1         = $resultado[$cq->bd_eps_positivo_q1];
			$objeto->eps_positivo_q2         = $resultado[$cq->bd_eps_positivo_q2];
			$objeto->eps_positivo_q3         = $resultado[$cq->bd_eps_positivo_q3];
			$objeto->eps_porc_change_q1      = $resultado[$cq->bd_eps_porc_change_q1];
			$objeto->eps_porc_change_q2      = $resultado[$cq->bd_eps_porc_change_q2];
			$objeto->eps_porc_change_q3      = $resultado[$cq->bd_eps_porc_change_q3];
			$objeto->eps_porc_change_avg     = $resultado[$cq->bd_eps_porc_change_avg];
			$objeto->eps_porc_surprise_q1    = $resultado[$cq->bd_eps_porc_surprise_q1];
			$objeto->eps_porc_surprise_q2    = $resultado[$cq->bd_eps_porc_surprise_q2];
			$objeto->eps_porc_surprise_q3    = $resultado[$cq->bd_eps_porc_surprise_q3];
			$objeto->eps_porc_surprise_avg   = $resultado[$cq->bd_eps_porc_surprise_avg];
			$objeto->sales_porc_change_q1    = $resultado[$cq->bd_sales_porc_change_q1];
			$objeto->sales_porc_change_q2    = $resultado[$cq->bd_sales_porc_change_q2];
			$objeto->sales_porc_change_q3    = $resultado[$cq->bd_sales_porc_change_q3];
			$objeto->sales_porc_change_avg   = $resultado[$cq->bd_sales_porc_change_avg];
			$objeto->sales_porc_surprise_q1  = $resultado[$cq->bd_sales_porc_surprise_q1];
			$objeto->sales_porc_surprise_q2  = $resultado[$cq->bd_sales_porc_surprise_q2];
			$objeto->sales_porc_surprise_q3  = $resultado[$cq->bd_sales_porc_surprise_q3];
			$objeto->sales_porc_surprise_avg = $resultado[$cq->bd_sales_porc_surprise_avg];
			$objeto->rsi                     = $resultado[$cq->bd_rsi];
			$objeto->sma_1mo_up              = $resultado[$cq->bd_sma_1mo_up];
			$objeto->sma_4mo_up              = $resultado[$cq->bd_sma_4mo_up];
			$objeto->base                    = $resultado[$cq->bd_base];
			$objeto->t1                      = $resultado[$cq->bd_t1];
			$objeto->t2                      = $resultado[$cq->bd_t2];
			$objeto->t3                      = $resultado[$cq->bd_t3];
			$objeto->caruso_bars             = $resultado[$cq->bd_caruso_bars];
			$objeto->puntos                  = $resultado[$cq->bd_puntos];
			$objeto->fecha_actualizado       = $resultado[$cq->bd_fecha_actualizado];
			$objeto->para_comprar            = $resultado[$cq->bd_para_comprar];
			$objeto->en_standby              = $resultado[$cq->bd_en_standby];
			$objeto->vetado                  = $resultado[$cq->bd_vetado];
			$objeto->con_posicion            = $resultado[$cq->bd_con_posicion];
			$objeto->estado                  = $resultado[$cq->bd_estado];
			$objeto->ultimo_tx               = $objeto->calcular_ultimo_tx();
			$objeto->porc_ultimo_tx          = $objeto->calcular_porc_ultimo_tx();
			$objeto->eps_positivos           = $objeto->calcular_eps_positivos() . '/3';
			
			$objeto->calcular_fecha_actualizado_dias();
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($paramref, PDO $conexion): self
	{
		try {
			$id     = (isset($paramref["id"])) ? $paramref["id"] : "";
			$ticker = (isset($paramref["ticker"])) ? $paramref["ticker"] : "";
			
			$cq         = new self;
			$cqa        = $cq->bd_alias;
			$cq_gru     = new TickerGrupo();
			$cq_gru_a   = $cq_gru->bd_alias;
			$cq_sugru   = new TickerSubGrupo();
			$cq_sugru_a = $cq_sugru->bd_alias;
			
			$query = "SELECT ";
			$query .= "   $cqa.* ";
			$query .= "  ,$cq_gru_a.$cq_gru->bd_nombre $cq->bd_f_nombre_ticker_grupo ";
			$query .= "  ,$cq_sugru_a.$cq_sugru->bd_nombre $cq->bd_f_nombre_ticker_subgrupo ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "INNER JOIN $cq_gru->bd_table $cq_gru_a ";
			$query .= "  ON ($cq_gru_a.$cq_gru->bd_id = $cqa.$cq->bd_id_ticker_grupo) ";
			$query .= "INNER JOIN $cq_sugru->bd_table $cq_sugru_a ";
			$query .= "  ON ($cq_sugru_a.$cq_sugru->bd_id = $cqa.$cq->bd_id_ticker_subgrupo) ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id > 0 ";
			
			if(!empty($id)){
				$query .= "AND $cqa.$cq->bd_id = :$cq->bd_id ";
			}
			if(!empty($ticker)){
				$query .= "AND $cqa.$cq->bd_nombre = :$cq->bd_nombre ";
			}
			
			$statement = $conexion->prepare($query);
			
			if(!empty($id)){
				$statement->bindValue(":$cq->bd_id", ordena($id));
			}
			if(!empty($ticker)){
				$statement->bindValue(":$cq->bd_nombre", $ticker);
			}
			
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list(array $paramref, PDO $conexion): array
	{
		try {
			$id_grupo           = (isset($paramref['id_grupo'])) ? $paramref['id_grupo'] : "";
			$nombre             = (isset($paramref['nombre'])) ? $paramref['nombre'] : "";
			$orderby_puntos     = (isset($paramref['orderby_puntos'])) ? $paramref['orderby_puntos'] : 0;
			$include_not_active = (isset($paramref['include_not_active'])) ? $paramref['include_not_active'] : 0;
			
			$cq = new self;
			$cqa = $cq->bd_alias;
			$cq_subgru = new TickerSubGrupo();
			$cq_subgru_a = $cq_subgru->bd_alias;
			
			$query = "SELECT ";
			$query .= "   $cqa.* ";
			$query .= "  ,$cq_subgru_a.$cq_subgru->bd_nombre $cq->bd_f_nombre_ticker_subgrupo ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "INNER JOIN $cq_subgru->bd_table $cq_subgru_a ";
			$query .= "  ON ($cq_subgru_a.$cq_subgru->bd_id = $cqa.$cq->bd_id_ticker_subgrupo) ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id > 0 ";
			$query .= "  AND $cqa.$cq->bd_vetado = 0 ";
			
			if($include_not_active == 0){
				$query .= "AND $cqa.$cq->bd_estado = 1 ";
			}
			if(!empty($id_grupo)){
				$query .= "AND $cqa.$cq->bd_id_ticker_grupo = :$cq->bd_id_ticker_grupo ";
			}
			if(!empty($nombre)){
				$query .= "AND $cqa.$cq->bd_nombre = :$cq->bd_nombre ";
			}
			
			if($orderby_puntos == 1){
				$query .= "ORDER BY ";
				$query .= "  $cqa.$cq->bd_puntos DESC ";
			}
			
			$statement = $conexion->prepare($query);
			
			if(!empty($id_grupo)){
				$statement->bindValue(":$cq->bd_id_ticker_grupo", ordena($id_grupo));
			}
			if(!empty($nombre)){
				$statement->bindValue(":$cq->bd_nombre", mb_strtoupper($nombre));
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function agregar(PDO $conexion): void
	{
		try {
			$this->validar_data_agregar($conexion);
			$this->calcular_promedios();
			$this->calcular_puntos();
			
			$cq = new self;
			
			$query = "INSERT INTO $cq->bd_table (";
			$query .= "   $cq->bd_nombre ";
			$query .= "  ,$cq->bd_id_ticker_grupo ";
			$query .= "  ,$cq->bd_id_ticker_subgrupo ";
			$query .= "  ,$cq->bd_next_eps ";
			$query .= "  ,$cq->bd_eps_positivo_q1 ";
			$query .= "  ,$cq->bd_eps_positivo_q2 ";
			$query .= "  ,$cq->bd_eps_positivo_q3 ";
			$query .= "  ,$cq->bd_eps_porc_change_q1 ";
			$query .= "  ,$cq->bd_eps_porc_change_q2 ";
			$query .= "  ,$cq->bd_eps_porc_change_q3 ";
			$query .= "  ,$cq->bd_eps_porc_change_avg ";
			$query .= "  ,$cq->bd_eps_porc_surprise_q1 ";
			$query .= "  ,$cq->bd_eps_porc_surprise_q2 ";
			$query .= "  ,$cq->bd_eps_porc_surprise_q3 ";
			$query .= "  ,$cq->bd_eps_porc_surprise_avg ";
			$query .= "  ,$cq->bd_sales_porc_change_q1 ";
			$query .= "  ,$cq->bd_sales_porc_change_q2 ";
			$query .= "  ,$cq->bd_sales_porc_change_q3 ";
			$query .= "  ,$cq->bd_sales_porc_change_avg ";
			$query .= "  ,$cq->bd_sales_porc_surprise_q1 ";
			$query .= "  ,$cq->bd_sales_porc_surprise_q2 ";
			$query .= "  ,$cq->bd_sales_porc_surprise_q3 ";
			$query .= "  ,$cq->bd_sales_porc_surprise_avg ";
			$query .= "  ,$cq->bd_rsi ";
			$query .= "  ,$cq->bd_sma_1mo_up ";
			$query .= "  ,$cq->bd_sma_4mo_up ";
			$query .= "  ,$cq->bd_base ";
			$query .= "  ,$cq->bd_t1 ";
			$query .= "  ,$cq->bd_t2 ";
			$query .= "  ,$cq->bd_t3 ";
			$query .= "  ,$cq->bd_caruso_bars ";
			$query .= "  ,$cq->bd_puntos ";
			$query .= "  ,$cq->bd_fecha_actualizado ";
			$query .= ") VALUES (";
			$query .= "   :$cq->bd_nombre ";
			$query .= "  ,:$cq->bd_id_ticker_grupo ";
			$query .= "  ,:$cq->bd_id_ticker_subgrupo ";
			$query .= "  ,:$cq->bd_next_eps ";
			$query .= "  ,:$cq->bd_eps_positivo_q1 ";
			$query .= "  ,:$cq->bd_eps_positivo_q2 ";
			$query .= "  ,:$cq->bd_eps_positivo_q3 ";
			$query .= "  ,:$cq->bd_eps_porc_change_q1 ";
			$query .= "  ,:$cq->bd_eps_porc_change_q2 ";
			$query .= "  ,:$cq->bd_eps_porc_change_q3 ";
			$query .= "  ,:$cq->bd_eps_porc_change_avg ";
			$query .= "  ,:$cq->bd_eps_porc_surprise_q1 ";
			$query .= "  ,:$cq->bd_eps_porc_surprise_q2 ";
			$query .= "  ,:$cq->bd_eps_porc_surprise_q3 ";
			$query .= "  ,:$cq->bd_eps_porc_surprise_avg ";
			$query .= "  ,:$cq->bd_sales_porc_change_q1 ";
			$query .= "  ,:$cq->bd_sales_porc_change_q2 ";
			$query .= "  ,:$cq->bd_sales_porc_change_q3 ";
			$query .= "  ,:$cq->bd_sales_porc_change_avg ";
			$query .= "  ,:$cq->bd_sales_porc_surprise_q1 ";
			$query .= "  ,:$cq->bd_sales_porc_surprise_q2 ";
			$query .= "  ,:$cq->bd_sales_porc_surprise_q3 ";
			$query .= "  ,:$cq->bd_sales_porc_surprise_avg ";
			$query .= "  ,:$cq->bd_rsi ";
			$query .= "  ,:$cq->bd_sma_1mo_up ";
			$query .= "  ,:$cq->bd_sma_4mo_up ";
			$query .= "  ,:$cq->bd_base ";
			$query .= "  ,:$cq->bd_t1 ";
			$query .= "  ,:$cq->bd_t2 ";
			$query .= "  ,:$cq->bd_t3 ";
			$query .= "  ,:$cq->bd_caruso_bars ";
			$query .= "  ,:$cq->bd_puntos ";
			$query .= "  ,:$cq->bd_fecha_actualizado ";
			$query .= ") ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_nombre", $this->nombre);
			$statement->bindValue(":$cq->bd_id_ticker_grupo", ordena($this->ticker_grupo->id));
			$statement->bindValue(":$cq->bd_id_ticker_subgrupo", ordena($this->ticker_subgrupo->id));
			$statement->bindValue(":$cq->bd_next_eps", $this->next_eps);
			$statement->bindValue(":$cq->bd_eps_positivo_q1", $this->eps_positivo_q1);
			$statement->bindValue(":$cq->bd_eps_positivo_q2", $this->eps_positivo_q2);
			$statement->bindValue(":$cq->bd_eps_positivo_q3", $this->eps_positivo_q3);
			$statement->bindValue(":$cq->bd_eps_porc_change_q1", $this->eps_porc_change_q1);
			$statement->bindValue(":$cq->bd_eps_porc_change_q2", $this->eps_porc_change_q2);
			$statement->bindValue(":$cq->bd_eps_porc_change_q3", $this->eps_porc_change_q3);
			$statement->bindValue(":$cq->bd_eps_porc_change_avg", $this->eps_porc_change_avg);
			$statement->bindValue(":$cq->bd_eps_porc_surprise_q1", $this->eps_porc_surprise_q1);
			$statement->bindValue(":$cq->bd_eps_porc_surprise_q2", $this->eps_porc_surprise_q2);
			$statement->bindValue(":$cq->bd_eps_porc_surprise_q3", $this->eps_porc_surprise_q3);
			$statement->bindValue(":$cq->bd_eps_porc_surprise_avg", $this->eps_porc_surprise_avg);
			$statement->bindValue(":$cq->bd_sales_porc_change_q1", $this->sales_porc_change_q1);
			$statement->bindValue(":$cq->bd_sales_porc_change_q2", $this->sales_porc_change_q2);
			$statement->bindValue(":$cq->bd_sales_porc_change_q3", $this->sales_porc_change_q3);
			$statement->bindValue(":$cq->bd_sales_porc_change_avg", $this->sales_porc_change_avg);
			$statement->bindValue(":$cq->bd_sales_porc_surprise_q1", $this->sales_porc_surprise_q1);
			$statement->bindValue(":$cq->bd_sales_porc_surprise_q2", $this->sales_porc_surprise_q2);
			$statement->bindValue(":$cq->bd_sales_porc_surprise_q3", $this->sales_porc_surprise_q3);
			$statement->bindValue(":$cq->bd_sales_porc_surprise_avg", $this->sales_porc_surprise_avg);
			$statement->bindValue(":$cq->bd_rsi", $this->rsi);
			$statement->bindValue(":$cq->bd_sma_1mo_up", $this->sma_1mo_up);
			$statement->bindValue(":$cq->bd_sma_4mo_up", $this->sma_4mo_up);
			$statement->bindValue(":$cq->bd_base", $this->base);
			$statement->bindValue(":$cq->bd_t1", $this->t1);
			$statement->bindValue(":$cq->bd_t2", $this->t2);
			$statement->bindValue(":$cq->bd_t3", $this->t3);
			$statement->bindValue(":$cq->bd_caruso_bars", $this->caruso_bars);
			$statement->bindValue(":$cq->bd_puntos", $this->puntos);
			$statement->bindValue(":$cq->bd_fecha_actualizado", $this->fecha_actualizado);
			$statement->execute();
			
			$this->id = desordena($conexion->lastInsertId());
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modificar(PDO $conexion): void
	{
		try {
			$this->validar_data_modificar($conexion);
			$this->calcular_promedios();
			$this->calcular_puntos();
			
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_id_ticker_grupo = :$cq->bd_id_ticker_grupo ";
			$query .= "  ,$cq->bd_id_ticker_subgrupo = :$cq->bd_id_ticker_subgrupo ";
			$query .= "  ,$cq->bd_next_eps = :$cq->bd_next_eps ";
			$query .= "  ,$cq->bd_eps_positivo_q1 = :$cq->bd_eps_positivo_q1 ";
			$query .= "  ,$cq->bd_eps_positivo_q2 = :$cq->bd_eps_positivo_q2 ";
			$query .= "  ,$cq->bd_eps_positivo_q3 = :$cq->bd_eps_positivo_q3 ";
			$query .= "  ,$cq->bd_eps_porc_change_q1 = :$cq->bd_eps_porc_change_q1 ";
			$query .= "  ,$cq->bd_eps_porc_change_q2 = :$cq->bd_eps_porc_change_q2 ";
			$query .= "  ,$cq->bd_eps_porc_change_q3 = :$cq->bd_eps_porc_change_q3 ";
			$query .= "  ,$cq->bd_eps_porc_change_avg = :$cq->bd_eps_porc_change_avg ";
			$query .= "  ,$cq->bd_eps_porc_surprise_q1 = :$cq->bd_eps_porc_surprise_q1 ";
			$query .= "  ,$cq->bd_eps_porc_surprise_q2 = :$cq->bd_eps_porc_surprise_q2 ";
			$query .= "  ,$cq->bd_eps_porc_surprise_q3 = :$cq->bd_eps_porc_surprise_q3 ";
			$query .= "  ,$cq->bd_eps_porc_surprise_avg = :$cq->bd_eps_porc_surprise_avg ";
			$query .= "  ,$cq->bd_sales_porc_change_q1 = :$cq->bd_sales_porc_change_q1 ";
			$query .= "  ,$cq->bd_sales_porc_change_q2 = :$cq->bd_sales_porc_change_q2 ";
			$query .= "  ,$cq->bd_sales_porc_change_q3 = :$cq->bd_sales_porc_change_q3 ";
			$query .= "  ,$cq->bd_sales_porc_change_avg = :$cq->bd_sales_porc_change_avg ";
			$query .= "  ,$cq->bd_sales_porc_surprise_q1 = :$cq->bd_sales_porc_surprise_q1 ";
			$query .= "  ,$cq->bd_sales_porc_surprise_q2 = :$cq->bd_sales_porc_surprise_q2 ";
			$query .= "  ,$cq->bd_sales_porc_surprise_q3 = :$cq->bd_sales_porc_surprise_q3 ";
			$query .= "  ,$cq->bd_sales_porc_surprise_avg = :$cq->bd_sales_porc_surprise_avg ";
			$query .= "  ,$cq->bd_rsi = :$cq->bd_rsi ";
			$query .= "  ,$cq->bd_sma_1mo_up = :$cq->bd_sma_1mo_up ";
			$query .= "  ,$cq->bd_sma_4mo_up = :$cq->bd_sma_4mo_up ";
			$query .= "  ,$cq->bd_base = :$cq->bd_base ";
			$query .= "  ,$cq->bd_t1 = :$cq->bd_t1 ";
			$query .= "  ,$cq->bd_t2 = :$cq->bd_t2 ";
			$query .= "  ,$cq->bd_t3 = :$cq->bd_t3 ";
			$query .= "  ,$cq->bd_caruso_bars = :$cq->bd_caruso_bars ";
			$query .= "  ,$cq->bd_puntos = :$cq->bd_puntos ";
			$query .= "  ,$cq->bd_fecha_actualizado = :$cq->bd_fecha_actualizado ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id_ticker_grupo", ordena($this->ticker_grupo->id));
			$statement->bindValue(":$cq->bd_id_ticker_subgrupo", ordena($this->ticker_subgrupo->id));
			$statement->bindValue(":$cq->bd_next_eps", $this->next_eps);
			$statement->bindValue(":$cq->bd_eps_positivo_q1", $this->eps_positivo_q1);
			$statement->bindValue(":$cq->bd_eps_positivo_q2", $this->eps_positivo_q2);
			$statement->bindValue(":$cq->bd_eps_positivo_q3", $this->eps_positivo_q3);
			$statement->bindValue(":$cq->bd_eps_porc_change_q1", $this->eps_porc_change_q1);
			$statement->bindValue(":$cq->bd_eps_porc_change_q2", $this->eps_porc_change_q2);
			$statement->bindValue(":$cq->bd_eps_porc_change_q3", $this->eps_porc_change_q3);
			$statement->bindValue(":$cq->bd_eps_porc_change_avg", $this->eps_porc_change_avg);
			$statement->bindValue(":$cq->bd_eps_porc_surprise_q1", $this->eps_porc_surprise_q1);
			$statement->bindValue(":$cq->bd_eps_porc_surprise_q2", $this->eps_porc_surprise_q2);
			$statement->bindValue(":$cq->bd_eps_porc_surprise_q3", $this->eps_porc_surprise_q3);
			$statement->bindValue(":$cq->bd_eps_porc_surprise_avg", $this->eps_porc_surprise_avg);
			$statement->bindValue(":$cq->bd_sales_porc_change_q1", $this->sales_porc_change_q1);
			$statement->bindValue(":$cq->bd_sales_porc_change_q2", $this->sales_porc_change_q2);
			$statement->bindValue(":$cq->bd_sales_porc_change_q3", $this->sales_porc_change_q3);
			$statement->bindValue(":$cq->bd_sales_porc_change_avg", $this->sales_porc_change_avg);
			$statement->bindValue(":$cq->bd_sales_porc_surprise_q1", $this->sales_porc_surprise_q1);
			$statement->bindValue(":$cq->bd_sales_porc_surprise_q2", $this->sales_porc_surprise_q2);
			$statement->bindValue(":$cq->bd_sales_porc_surprise_q3", $this->sales_porc_surprise_q3);
			$statement->bindValue(":$cq->bd_sales_porc_surprise_avg", $this->sales_porc_surprise_avg);
			$statement->bindValue(":$cq->bd_rsi", $this->rsi);
			$statement->bindValue(":$cq->bd_sma_1mo_up", $this->sma_1mo_up);
			$statement->bindValue(":$cq->bd_sma_4mo_up", $this->sma_4mo_up);
			$statement->bindValue(":$cq->bd_base", $this->base);
			$statement->bindValue(":$cq->bd_t1", $this->t1);
			$statement->bindValue(":$cq->bd_t2", $this->t2);
			$statement->bindValue(":$cq->bd_t3", $this->t3);
			$statement->bindValue(":$cq->bd_caruso_bars", $this->caruso_bars);
			$statement->bindValue(":$cq->bd_puntos", $this->puntos);
			$statement->bindValue(":$cq->bd_fecha_actualizado", $this->fecha_actualizado);
			$statement->bindValue(":$cq->bd_id", ordena($this->id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modificar_para_comprar($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_para_comprar = $cq->bd_para_comprar ^ 1 ";
			$query .= "  ,$cq->bd_en_standby = 0 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modificar_en_standby($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_en_standby = $cq->bd_en_standby ^ 1 ";
			$query .= "  ,$cq->bd_para_comprar = 0 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modificar_vetado($paramref, PDO $conexion): void
	{
		try {
			$id     = (isset($paramref["id"])) ? $paramref["id"] : "";
			$ticker = (isset($paramref["ticker"])) ? $paramref["ticker"] : "";
			
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_vetado = $cq->bd_vetado ^ 1 ";
			$query .= "WHERE ";
			
			if(!empty($id)){
				$query .= "  $cq->bd_id = :$cq->bd_id ";
			}
			if(!empty($ticker)){
				$query .= "  $cq->bd_nombre = :$cq->bd_nombre ";
			}
			
			$statement = $conexion->prepare($query);
			
			if(!empty($id)){
				$statement->bindValue(":$cq->bd_id", ordena($id));
			}
			if(!empty($ticker)){
				$statement->bindValue(":$cq->bd_nombre", $ticker);
			}
			
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modificar_con_posicion($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_en_standby = 0 ";
			$query .= "  ,$cq->bd_para_comprar = 0 ";
			$query .= "  ,$cq->bd_con_posicion = 1 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function eliminar($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_estado = 0 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calcular_promedios(): void
	{
	    try {
			$this->eps_porc_change_avg     = round(($this->eps_porc_change_q1 + $this->eps_porc_change_q2 + $this->eps_porc_change_q3) / 3, 0);
			$this->eps_porc_surprise_avg   = round(($this->eps_porc_surprise_q1 + $this->eps_porc_surprise_q2 + $this->eps_porc_surprise_q3) / 3, 0);
		    $this->sales_porc_change_avg   = round(($this->sales_porc_change_q1 + $this->sales_porc_change_q2 + $this->sales_porc_change_q3) / 3, 0);
		    $this->sales_porc_surprise_avg = round(($this->sales_porc_surprise_q1 + $this->sales_porc_surprise_q2 + $this->sales_porc_surprise_q3) / 3, 0);
		    
	    } catch (Exception $e) {
	        throw new Exception($e->getMessage());
	    }
	}
	
	/**
	 * @throws Exception
	 */
	private function calcular_puntos(): void
	{
		try {
			$this->puntos = 0;
			
			#region region eps positivo
			if ($this->eps_positivo_q1 == 0) {
				$this->puntos += -1;
			}
			if ($this->eps_positivo_q2 == 0) {
				$this->puntos += -1;
			}
			if ($this->eps_positivo_q3 == 0) {
				$this->puntos += -1;
			}
			#endregion eps positivo
			#region region eps change
			if ($this->eps_porc_change_q1 <= 0) {
				$this->puntos += -1;
			}
			if ($this->eps_porc_change_q1 >= 15) {
				$this->puntos += 1;
			}
			if ($this->eps_porc_change_q1 >= 20) {
				$this->puntos += 1;
			}
			if ($this->eps_porc_change_q2 <= 0) {
				$this->puntos += -1;
			}
			if ($this->eps_porc_change_q2 >= 15) {
				$this->puntos += 1;
			}
			if ($this->eps_porc_change_q2 >= 20) {
				$this->puntos += 1;
			}
			if($this->eps_porc_change_q2 > 0 && $this->eps_porc_change_q2 > $this->eps_porc_change_q1){
				$this->puntos += 1;
			}
			if ($this->eps_porc_change_q3 <= 0) {
				$this->puntos += -1;
			}
			if ($this->eps_porc_change_q3 >= 15) {
				$this->puntos += 1;
			}
			if ($this->eps_porc_change_q3 >= 20) {
				$this->puntos += 1;
			}
			if($this->eps_porc_change_q3 > 0 && $this->eps_porc_change_q3 > $this->eps_porc_change_q2){
				$this->puntos += 1;
			}
			if ($this->eps_porc_change_avg <= 0) {
				$this->puntos += -1;
			}
			if ($this->eps_porc_change_avg >= 15) {
				$this->puntos += 1;
			}
			if ($this->eps_porc_change_avg >= 20) {
				$this->puntos += 1;
			}
			#endregion eps change
			#region region eps surprise
			if ($this->eps_porc_surprise_q1 <= 0) {
				$this->puntos += -1;
			}
			if ($this->eps_porc_surprise_q1 > 0) {
				$this->puntos += 1;
			}
			if ($this->eps_porc_surprise_q2 <= 0) {
				$this->puntos += -1;
			}
			if ($this->eps_porc_surprise_q2 > 0) {
				$this->puntos += 1;
			}
			if ($this->eps_porc_surprise_q3 <= 0) {
				$this->puntos += -1;
			}
			if ($this->eps_porc_surprise_q3 > 0) {
				$this->puntos += 1;
			}
			if ($this->eps_porc_surprise_avg <= 0) {
				$this->puntos += -1;
			}
			if ($this->eps_porc_surprise_avg > 0) {
				$this->puntos += 1;
			}
			#endregion eps surprise
			#region region sales change
			if ($this->sales_porc_change_q1 <= 0) {
				$this->puntos += -1;
			}
			if ($this->sales_porc_change_q1 >= 15) {
				$this->puntos += 1;
			}
			if ($this->sales_porc_change_q1 >= 20) {
				$this->puntos += 1;
			}
			if ($this->sales_porc_change_q2 <= 0) {
				$this->puntos += -1;
			}
			if ($this->sales_porc_change_q2 >= 15) {
				$this->puntos += 1;
			}
			if ($this->sales_porc_change_q2 >= 20) {
				$this->puntos += 1;
			}
			if($this->sales_porc_change_q2 > 0 && $this->sales_porc_change_q2 > $this->sales_porc_change_q1){
				$this->puntos += 1;
			}
			if ($this->sales_porc_change_q3 <= 0) {
				$this->puntos += -1;
			}
			if ($this->sales_porc_change_q3 >= 15) {
				$this->puntos += 1;
			}
			if ($this->sales_porc_change_q3 >= 20) {
				$this->puntos += 1;
			}
			if($this->sales_porc_change_q3 > 0 && $this->sales_porc_change_q3 > $this->sales_porc_change_q2){
				$this->puntos += 1;
			}
			if ($this->sales_porc_change_avg <= 0) {
				$this->puntos += -1;
			}
			if ($this->sales_porc_change_avg >= 15) {
				$this->puntos += 1;
			}
			if ($this->sales_porc_change_avg >= 20) {
				$this->puntos += 1;
			}
			#endregion sales change
			#region region sales surprise
			if ($this->sales_porc_surprise_q1 <= 0) {
				$this->puntos += -1;
			}
			if ($this->sales_porc_surprise_q1 > 0) {
				$this->puntos += 1;
			}
			if ($this->sales_porc_surprise_q2 <= 0) {
				$this->puntos += -1;
			}
			if ($this->sales_porc_surprise_q2 > 0) {
				$this->puntos += 1;
			}
			if ($this->sales_porc_surprise_q3 <= 0) {
				$this->puntos += -1;
			}
			if ($this->sales_porc_surprise_q3 > 0) {
				$this->puntos += 1;
			}
			if ($this->sales_porc_surprise_avg <= 0) {
				$this->puntos += -1;
			}
			if ($this->sales_porc_surprise_avg > 0) {
				$this->puntos += 1;
			}
			#endregion sales surprise
			#region region analisis tecnico
			if ($this->rsi > 70) {
				$this->puntos += 1;
			}
			
			if ($this->sma_1mo_up == 1) {
				$this->puntos += 1;
			}
			if ($this->sma_4mo_up == 1) {
				$this->puntos += 1;
			}
			
			if ($this->base > 35) {
				$this->puntos += -1;
			}
			if ($this->base <= 35) {
				$this->puntos += 1;
			}
			
			if ($this->t1 > 0 && $this->t1 < $this->base) {
				$this->puntos += 1;
			}
			if ($this->t2 > 0 && $this->t2 < $this->t1) {
				$this->puntos += 1;
			}
			if ($this->t3 > 0 && $this->t3 < $this->t2) {
				$this->puntos += 1;
			}
			
			$this->ultimo_tx = $this->calcular_ultimo_tx();
			
			if($this->ultimo_tx > 0 && $this->ultimo_tx <= self::MINIMO_ULTIMO_TX){
				$this->puntos += 1;
			}
			#endregion analisis tecnico
			
			$this->puntos += $this->caruso_bars;
		
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function calcular_porc_ultimo_tx(): float|int
	{
	    try {
			if($this->t3 > 0){
				return $this->t3;
			}
		    if($this->t2 > 0){
			    return $this->t2;
		    }
		    if($this->t1 > 0){
			    return $this->t1;
		    }
		    if($this->base > 0){
			    return $this->base;
		    }
			
			return 0;
	    
	    } catch (Exception $e) {
	        throw new Exception($e->getMessage());
	    }
	}
	
	/**
	 * @throws Exception
	 */
	private function calcular_ultimo_tx(): string
	{
	    try {
			if($this->t3 > 0){
				return 'T3';
			}
		    if($this->t2 > 0){
			    return 'T2';
		    }
		    if($this->t1 > 0){
			    return 'T1';
		    }
		    if($this->base > 0){
			    return 'BASE';
		    }
			
			return 'N/A';
	    
	    } catch (Exception $e) {
	        throw new Exception($e->getMessage());
	    }
	}
	
	/**
	 * @throws Exception
	 */
	private function calcular_eps_positivos(): int
	{
	    try {
			return $this->eps_positivo_q1 + $this->eps_positivo_q2 + $this->eps_positivo_q3;
	    
	    } catch (Exception $e) {
	        throw new Exception($e->getMessage());
	    }
	}
	
	/**
	 * @throws Exception
	 */
	private function calcular_fecha_actualizado_dias(): void
	{
	    try {
			$fecha_actual                 = create_date();
	        $this->fecha_actualizado_dias = getDateDiffDays($fecha_actual, $this->fecha_actualizado);
	        
	    } catch (Exception $e) {
	        throw new Exception($e->getMessage());
	    }
	}
	
	/**
	 * @throws Exception
	 */
	private function validar_data_agregar($conexion): void
	{
		try {
			validar_campovacio($this->nombre, 'Debe especificar el nombre');
			validar_campovacio($this->ticker_grupo->nombre, 'Debe especificar el grupo');
			validar_campovacio($this->ticker_subgrupo->nombre, 'Debe especificar el subgrupo');
			validar_campovacio($this->next_eps, 'Debe especificar la fecha de earnings');
			
			//validar si el ticker existe en el sistema.
			$param           = array();
			$param['nombre'] = $this->nombre;
			$tickers         = Ticker::get_list($param, $conexion);
			
			if(count($tickers) > 0){
				throw new Exception('El ticker ya existe en el sistema');
			}
			
			$this->ticker_grupo->id    = TickerGrupo::get_bynombre($this->ticker_grupo->nombre, $conexion)->id;
			$this->ticker_subgrupo->id = TickerSubGrupo::get_bynombre($this->ticker_subgrupo->nombre, $conexion)->id;
			
			if(empty($this->ticker_grupo->id)){
				throw new Exception('El grupo no existe en el sistema');
			}
			if(empty($this->ticker_subgrupo->id)){
				throw new Exception('El subgrupo no existe en el sistema');
			}
			
			$this->nombre              = mb_strtoupper(trim($this->nombre), 'UTF-8');
			$this->fecha_actualizado   = create_date();
		
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validar_data_modificar($conexion): void
	{
		try {
			validar_campovacio($this->ticker_grupo->nombre, 'Debe especificar el grupo');
			validar_campovacio($this->ticker_subgrupo->nombre, 'Debe especificar el subgrupo');
			validar_campovacio($this->next_eps, 'Debe especificar la fecha de earnings');
			
			$this->ticker_grupo->id    = TickerGrupo::get_bynombre($this->ticker_grupo->nombre, $conexion)->id;
			$this->ticker_subgrupo->id = TickerSubGrupo::get_bynombre($this->ticker_subgrupo->nombre, $conexion)->id;
			$this->nombre              = mb_strtoupper(trim($this->nombre), 'UTF-8');
			$this->fecha_actualizado   = create_date();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>