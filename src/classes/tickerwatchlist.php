<?php

class TickerWatchlist
{
	public string $id;
	public string $ticker;
	public string $estado;
	private string $bd_table  = 'tickers_watchlist';
	private string $bd_alias  = 'tirwat';
	private string $bd_id     = 'id';
	private string $bd_ticker = 'ticker';
	private string $bd_estado = 'estado';
	
	function __construct()
	{
		$this->id     = '';
		$this->ticker = '';
		$this->estado = 0;
	}
	
	/**
	 * @param $resultado
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$cq = new self;
			
			$objeto                = new self;
			$objeto->id            = desordena($resultado[$cq->bd_id]);
			$objeto->ticker        = $resultado[$cq->bd_ticker];
			$objeto->estado        = $resultado[$cq->bd_estado];
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, PDO $conexion): self
	{
		try {
			$cq = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list(array $paramref, PDO $conexion): array
	{
		try {
			$cq        = new self;
			$cqa       = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id > 0 ";
			$query .= "ORDER BY ";
			$query .= "  $cqa.$cq->bd_ticker ";
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function agregar(PDO $conexion): void
	{
		try {
			$this->validar_data();
			
			$cq = new self;
			
			$query = "INSERT INTO $cq->bd_table (";
			$query .= "   $cq->bd_ticker ";
			$query .= "  ,$cq->bd_estado ";
			$query .= ") VALUES (";
			$query .= "   :$cq->bd_ticker ";
			$query .= "  ,:$cq->bd_estado ";
			$query .= ") ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_ticker", $this->ticker);
			$statement->bindValue(":$cq->bd_estado", $this->estado);
			$statement->execute();
			
			$this->id = desordena($conexion->lastInsertId());
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function eliminar($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " DELETE FROM $cq->bd_table ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function eliminar_todos(PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " DELETE FROM $cq->bd_table ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id > 0 ";
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validar_data(): void
	{
		try {
		
		
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>