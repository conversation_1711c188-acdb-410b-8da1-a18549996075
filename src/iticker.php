<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/ticker.php';
require_once __ROOT__ . '/src/classes/tickergrupo.php';
require_once __ROOT__ . '/src/classes/tickersubgrupo.php';
require_once __ROOT__ . '/src/general/preparar.php';

$new_ticker = new Ticker();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_SESSION['search_ticker'])) {
			$new_ticker->nombre = $_SESSION['search_ticker'];
			
			unset($_SESSION['search_ticker']);
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$new_ticker->nombre                  = limpiar_datos($_POST['nombre']);
		$new_ticker->ticker_grupo->nombre    = limpiar_datos($_POST['grupo']);
		$new_ticker->ticker_subgrupo->nombre = limpiar_datos($_POST['subgrupo']);
		$new_ticker->next_eps                = limpiar_datos($_POST['next_eps']);
		$new_ticker->eps_positivo_q1         = @getvalue_checkbox($_POST['eps_positivo_q1']);
		$new_ticker->eps_positivo_q2         = @getvalue_checkbox($_POST['eps_positivo_q2']);
		$new_ticker->eps_positivo_q3         = @getvalue_checkbox($_POST['eps_positivo_q3']);
		$new_ticker->eps_porc_change_q1      = limpiar_datos_numero($_POST['eps_porc_change_q1']);
		$new_ticker->eps_porc_change_q2      = limpiar_datos_numero($_POST['eps_porc_change_q2']);
		$new_ticker->eps_porc_change_q3      = limpiar_datos_numero($_POST['eps_porc_change_q3']);
		$new_ticker->eps_porc_surprise_q1    = limpiar_datos_numero($_POST['eps_porc_surprise_q1']);
		$new_ticker->eps_porc_surprise_q2    = limpiar_datos_numero($_POST['eps_porc_surprise_q2']);
		$new_ticker->eps_porc_surprise_q3    = limpiar_datos_numero($_POST['eps_porc_surprise_q3']);
		$new_ticker->sales_porc_change_q1    = limpiar_datos_numero($_POST['sales_porc_change_q1']);
		$new_ticker->sales_porc_change_q2    = limpiar_datos_numero($_POST['sales_porc_change_q2']);
		$new_ticker->sales_porc_change_q3    = limpiar_datos_numero($_POST['sales_porc_change_q3']);
		$new_ticker->sales_porc_surprise_q1  = limpiar_datos_numero($_POST['sales_porc_surprise_q1']);
		$new_ticker->sales_porc_surprise_q2  = limpiar_datos_numero($_POST['sales_porc_surprise_q2']);
		$new_ticker->sales_porc_surprise_q3  = limpiar_datos_numero($_POST['sales_porc_surprise_q3']);
		$new_ticker->rsi                     = limpiar_datos_numero($_POST['rsi']);
		$new_ticker->sma_1mo_up              = @getvalue_checkbox($_POST['sma_1mo_up']);
		$new_ticker->sma_4mo_up              = @getvalue_checkbox($_POST['sma_4mo_up']);
		$new_ticker->base                    = limpiar_datos_numero($_POST['base']);
		$new_ticker->t1                      = limpiar_datos_numero($_POST['t1']);
		$new_ticker->t2                      = limpiar_datos_numero($_POST['t2']);
		$new_ticker->t3                      = limpiar_datos_numero($_POST['t3']);
		$new_ticker->caruso_bars             = limpiar_datos_numero($_POST['caruso_bars']);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion postsolo
#region sub_crear_grupo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_crear_grupo'])) {
	try {
		$crear_grupo_nombre = limpiar_datos($_POST['crear_grupo_nombre']);
		
		$new_ticker_grupo         = new TickerGrupo();
		$new_ticker_grupo->nombre = $crear_grupo_nombre;
		$new_ticker_grupo->agregar($conexion);
		
		$success_display = 'show';
		$success_text = 'El grupo ha sido creado.';
	
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_crear_grupo
#region sub_crear_subgrupo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_crear_subgrupo'])) {
	try {
		$crear_subgrupo_nombre = limpiar_datos($_POST['crear_subgrupo_nombre']);
		
		$new_ticker_subgrupo         = new TickerSubGrupo();
		$new_ticker_subgrupo->nombre = $crear_subgrupo_nombre;
		$new_ticker_subgrupo->agregar($conexion);
		
		$success_display = 'show';
		$success_text = 'El subgrupo ha sido creado.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_crear_subgrupo
#region sub_crear
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_crear'])) {
	try {
		$new_ticker->agregar($conexion);
		
		$_SESSION['id_grupo'] = $new_ticker->ticker_grupo->id;
		$_SESSION['ticker']   = $new_ticker->nombre;
		
		header('Location: ltickers?i=1');
		exit();
	
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_crear
#region try
try {
	$tickers_grupos    = TickerGrupo::get_list(array(), $conexion);
	$tickers_subgrupos = TickerSubGrupo::get_list(array(), $conexion);

} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/iticker.view.php';

?>





