<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/objective.php';
require_once __ROOT__ . '/src/general/preparar.php';

$selectedtipo = '';
$selectedboss = '';

#region sub_selectgroup
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_selectgroup'])) {
    try {
        $_SESSION['selectedboss'] = limpiar_datos($_POST['selectedboss']);
        $_SESSION['selectedtipo'] = limpiar_datos($_POST['selectedtipo']);

        header('Location: lobjectives');

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_addobjective
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addobjective'])) {
    try {
        $_SESSION['selectedtipo'] = 'group';
        $_SESSION['selectedboss'] = 'group';

        header('Location: iobjective');

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region try
try {
    $bosses = Objective::get_listboss($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion

require_once __ROOT__ . '/views/lgroupobjectives.view.php';

?>