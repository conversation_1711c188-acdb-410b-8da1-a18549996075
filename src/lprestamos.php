<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

use App\classes\Prestamo;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/general/preparar.php';

$newprestamo = new Prestamo;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'El prestamo ha sido modificado.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        // Use setters and cast valor to float
        $newprestamo->setNombre(limpiar_datos($_POST['nombre']));
        $newprestamo->setValor((float)limpiar_datos($_POST['valor'])); // Cast to float
        $newprestamo->setTipo(limpiar_datos($_POST['tipo'])); // Set the tipo
        $newprestamo->setNotaAdicional(limpiar_datos($_POST['nota_adicional'])); // Set nota adicional

        // Set manual fecha_creacion if provided, otherwise use current date
        if (!empty($_POST['fecha_creacion'])) {
            $newprestamo->setFechaCreacion(limpiar_datos($_POST['fecha_creacion']));
        }

        // Use guardar method instead of add
        $newprestamo->guardar($conexion);

        $success_display = 'show';
        $success_text = 'El prestamo ha sido ingresado.';

        $newprestamo = new Prestamo();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add
#region sub_delprestamo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delprestamo'])) {
    try {
        $delidprestamo = limpiar_datos($_POST['mdl_delprestamo_idprestamo']);

        Prestamo::delete($delidprestamo, $conexion);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delprestamo
#region sub_resolveprestamo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_resolveprestamo'])) {
    try {
        $resolveidprestamo = limpiar_datos($_POST['mdl_resolveprestamo_idprestamo']);

        // Get the existing prestamo
        $prestamoToResolve = Prestamo::get($resolveidprestamo, $conexion);
        if (!$prestamoToResolve) {
            throw new Exception("No se encontró el préstamo a resolver.");
        }

        // Set resuelto to 1 (resolved)
        $prestamoToResolve->setResuelto(1);

        // Save the changes
        $prestamoToResolve->guardar($conexion);

        $success_display = 'show';
        $success_text = 'El préstamo ha sido marcado como resuelto.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_resolveprestamo
#region sub_editprestamo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editprestamo'])) {
    try {
        $_SESSION['idprestamo'] = limpiar_datos($_POST['selidprestamo']);

        header('Location: eprestamo');

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editprestamo
#region try
try {
    $prestamos    = Prestamo::getList($conexion);
    $totalsByType = Prestamo::getSumValorByType($prestamos); // Calculate totals by type

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lprestamos.view.php';

?>
