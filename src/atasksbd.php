<?php session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/taskbd.php';
require_once __ROOT__ . '/src/classes/project.php';
require_once __ROOT__ . '/src/general/preparar.php';

$tasksbd = array();
$updatelist_tasksbd = 0;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$id_project = limpiar_datos($_POST['id_project']);

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search'])) {
	try {
		validar_textovacio($id_project, 'Specify project');

		$updatelist_tasksbd = 1;

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
	try {
		$name = limpiar_datos($_POST['name']);

		validar_textovacio($id_project, 'Specify project');
		validar_textovacio($name, 'Specify task');
		
		$taskbd = new TaskBd;
		$taskbd->id_project = $id_project;
		$taskbd->name = $name;
		$taskbd->add($conexion);

		$name = "";
		$updatelist_tasksbd = 1;		

		$success_display = 'show';
		$success_text = 'Task has been added.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}

try {
	$projects = Project::get_list($conexion);

	if($updatelist_tasksbd == 1){
		$tasksbd = TaskBd::get_list($id_project,$conexion);
	}
} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}

require_once __ROOT__ . '/views/atasksbd.view.php';

?>
