<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'El canal ha sido modificado.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $newbudget = new Budget();
        $newbudget->canal = limpiar_datos($_POST['canal']);
        $newbudget->add($conexion);

        $success_display = 'show';
        $success_text = 'El canal ha sido agregado.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add
#region sub_delbudget
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delbudget'])) {
    try {
        $delidbudget = limpiar_datos($_POST['mdl_delbudget_idbudget']);

        Budget::delete($delidbudget, $conexion);

        $success_display = 'show';
        $success_text = 'El canal ha sido eliminado.';

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delbudget
#region sub_editbudget
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editbudget'])) {
    try {
        $_SESSION['idbudget'] = limpiar_datos($_POST['selidbudget']);

        header('Location: ebudget');

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editbudget
#region try
try {
    $param = array();
    $param['includetrk'] = 0;
    $budgets = Budget::getList($param,$conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lbudgets.view.php';

?>












