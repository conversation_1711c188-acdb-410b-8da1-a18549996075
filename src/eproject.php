<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/project.php';
require_once __ROOT__ . '/src/classes/projectabono.php';
require_once __ROOT__ . '/src/classes/projectmodulo.php';
require_once __ROOT__ . '/src/classes/projecttarea.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region region init variables
$modproject      = new Project;
$newprojectabono = new ProjectAbono;
$projectabonos   = array();
$tabselected     = 1;
$ckeditor_rows   = 50;
$projects_tareas = array();
#endregion init variables
#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['id_project'])) {
            $id_project = $_SESSION['id_project'];

            unset($_SESSION['id_project']);
        } else {
            header('Location: lprojects');
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $id_project  = limpiar_datos($_POST['id_project']);
        $tabselected = limpiar_datos($_POST["tabselected"]);
        $id_modulo   = limpiar_datos($_POST["id_modulo"]);
        
        $modproject->id_project      = $id_project;
        $modproject->name            = limpiar_datos(trim($_POST['project_name']));
        $modproject->bdtasks         = trim($_POST['bdtasks']);
        $modproject->notes           = trim($_POST['notes']);
        $modproject->valor_venta     = trim($_POST['valor_venta']);
        $modproject->forma_pago      = trim($_POST['forma_pago']);
        $modproject->division_profit = trim($_POST['division_profit']);
        $modproject->goals           = trim($_POST['goals']);
        $modproject->pendiente       = trim($_POST['pendiente']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_save
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_save'])) {
    try {
        $modproject->modify($conexion);

        $success_display = 'show';
        $success_text = 'Project modified.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_save
#region sub_add_abono
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add_abono'])) {
    try {
        $newprojectabono                      = new ProjectAbono();
        $newprojectabono->project->id_project = $id_project;
        $newprojectabono->fecha               = limpiar_datos($_POST['abono_fecha']);
        $newprojectabono->valor               = limpiar_datos($_POST['abono_valor']);
        $newprojectabono->add($conexion);

        $success_display = 'show';
        $success_text = 'Abono has been added.';
        
        $newprojectabono = new ProjectAbono;

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add_abono
#region sub_crear_modulo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_crear_modulo'])) {
    try {
        $new_project_modulo                      = new ProjectModulo();
        $new_project_modulo->project->id_project = $id_project;
        $new_project_modulo->nombre              = limpiar_datos($_POST['crear_modulo_nombre']);
        $new_project_modulo->descripcion         = limpiar_datos($_POST['crear_modulo_descripcion']);
        $new_project_modulo->agregar($conexion);
        
        $id_modulo = $new_project_modulo->id;
        
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_crear_modulo
#region sub_agregar_tarea
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_tarea'])) {
    try {
        $new_project_tarea              = new ProjectTarea();
        $new_project_tarea->modulo->id  = $id_modulo;
        $new_project_tarea->descripcion = limpiar_datos($_POST['tarea']);
        $new_project_tarea->agregar($conexion);
				
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_agregar_tarea
#region sub_marcar_tarea
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_marcar_tarea'])) {
    try {
        $id_tarea = limpiar_datos($_POST['sel_tarea']);
        
        ProjectTarea::modificar_hecho($id_tarea, $conexion);
				
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_marcar_tarea
#region sub_editar_notas
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editar_notas'])) {
    try {
        $modproject->modify($conexion);
				
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editar_notas
#region try
try {
    $modproject = Project::get($id_project, $conexion);

    $param = array();
    $param['id_project'] = $id_project;

    $projectabonos    = ProjectAbono::get_list($param, $conexion);
    $abonos_total     = Project::calculate_abono_total($projectabonos);
    $abonos_pendiente = Project::calculate_abono_pendiente($modproject->valor_venta, $abonos_total);
    
    $param               = array();
    $param['id_project'] = $id_project;
    $projects_modulos    = ProjectModulo::get_list($param, $conexion);
    
    //buscar tareas
    if(!empty($id_modulo)){
        $param              = array();
        $param['id_modulo'] = $id_modulo;
        $projects_tareas    = ProjectTarea::get_list($param, $conexion);
    }
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try
require_once __ROOT__ . '/views/eproject.view.php';

?>
    
