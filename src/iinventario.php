<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/inventario.php';
require_once __ROOT__ . '/src/classes/inventariocategoria.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $newinventario = new Inventario();
        $newinventario->inventariocategoria = new InventarioCategoria();
        $newinventario->inventariocategoria->id = limpiar_datos($_POST['idinventariocategoria']);
        $newinventario->nombre = limpiar_datos($_POST['nombre']);
        $newinventario->estadoqty = limpiar_datos($_POST['estadoqty']);
        $newinventario->add($conexion);

        $newinventario = new Inventario();

        $success_display = 'show';
        $success_text = 'El inventario ha sido agregado.';


    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add
#region try
try {
    $categorias = InventarioCategoria::getList($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/iinventario.view.php';

?>