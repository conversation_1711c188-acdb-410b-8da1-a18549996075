<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/apuestatipo.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$riesgo_porc = 0;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        if (isset($_SESSION['idpartido'])) {
            $idpartido = $_SESSION['idpartido'];
            $idtipoapuesta = $_SESSION['idtipoapuesta'];
            $valorapuesta = $_SESSION['valorapuesta'];
        
            unset($_SESSION['idpartido']);
            unset($_SESSION['idtipoapuesta']);
            unset($_SESSION['valorapuesta']);
        } else {
            header('Location: lpartidos');
            exit();
        }

        if (isset($_SESSION['winprobabilityporc'])){
            $winprobabilityporc = $_SESSION['winprobabilityporc'];
            
            unset($_SESSION['winprobabilityporc']);
        }
            
        if (isset($_SESSION['potencial'])) {
            $potencial = $_SESSION['potencial'];
            $riesgo = $_SESSION['riesgo'];
            $riesgo_porc = $_SESSION['riesgo_porc'];
        
            unset($_SESSION['potencial']);
            unset($_SESSION['riesgo']);
            unset($_SESSION['riesgo_porc']);
            
        } else{
            $riesgo = 0;
        }


    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $idpartido = limpiar_datos($_POST['idpartido']);
        $idtipoapuesta = limpiar_datos($_POST['idtipoapuesta']);
        $valorapuesta = limpiar_datos($_POST['valorapuesta']);
        $riesgo = limpiar_datos($_POST['riesgo']);
        $isprediccion = @getvalue_checkbox($_POST['isprediccion']);
        $isprediccionpremium = @getvalue_checkbox($_POST['isprediccionpremium']);
        $tipster = limpiar_datos($_POST['tipster']);
        $potencial = limpiar_datos($_POST['potencial']);
        $winprobabilityporc = limpiar_datos($_POST['winprobabilityporc']);
        $nota = limpiar_datos($_POST['nota']);
        $riesgo_porc = limpiar_datos($_POST['riesgo_porc']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_regresar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_regresar'])) {
    try {
        $method_sub_regresar = 1;
        
        $_SESSION['idpartido'] = $idpartido;

        header('Location: vpartidoinfo');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_regresar
#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $method_sub_add = 1;
        
        $newpartidoapuesta = new PartidoApuesta();
        $newpartidoapuesta->idpartido = $idpartido;
        $newpartidoapuesta->idapuestatipo = $idtipoapuesta;
        $newpartidoapuesta->valorapuesta = $valorapuesta;
        $newpartidoapuesta->riesgo = $riesgo;
        $newpartidoapuesta->potencial = $potencial;
        $newpartidoapuesta->porccrit1 = 0;
        $newpartidoapuesta->porccrit2 = 0;
        $newpartidoapuesta->porccrit3 = 0;
        $newpartidoapuesta->porccrit4 = 0;
        $newpartidoapuesta->porccrit5 = 0;
        $newpartidoapuesta->porccrit6 = 0;
        $newpartidoapuesta->porccrit7 = 0;
        $newpartidoapuesta->porccrit8 = 0;
        $newpartidoapuesta->porccrit9 = 0;
        $newpartidoapuesta->isprediccion = $isprediccion;
        $newpartidoapuesta->isprediccionpremium = $isprediccionpremium;
        $newpartidoapuesta->tipster = $tipster;
        $newpartidoapuesta->winprobabilityporc = $winprobabilityporc;
        $newpartidoapuesta->nota = $nota;
        $newpartidoapuesta->betsizekellycritporc = $riesgo_porc;
        $newpartidoapuesta->estado = 1;
        $newpartidoapuesta->add($conexion);  
        
        $_SESSION['idpartido'] = $idpartido;

        header('Location: vpartidoinfo?i=1');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add
#region sub_calcular
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_calcular'])) {
    try {
        $method_sub_calcular = 1;

        $resultado = PartidoApuesta::calculateBetSize($potencial, $winprobabilityporc, $conexion);
        $riesgo = $resultado['betsize_value'];
        $riesgo_porc = $resultado['betsize_porc'];

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_calcular
#region try
try {
    $method_try = 1;

    $modpartido = Partido::get($idpartido, $conexion);
    $actapuestatipo = ApuestaTipo::get($idtipoapuesta, $conexion);
    $tipsters = PartidoApuesta::getListTipsters($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/ipartidootrasapuestas.view.php';

?>