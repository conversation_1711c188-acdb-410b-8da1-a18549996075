<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/transaccion.php';
require_once __ROOT__ . '/src/classes/categoriatransaccion.php';
require_once __ROOT__ . '/src/classes/transaccioncategoria.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/general/preparar.php';

$tabselected = 1;
$modtransaccion = new Transaccion();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['idtransaccion'])) {
            $idtransaccion = $_SESSION['idtransaccion'];

            // logic:

            unset($_SESSION['idtransaccion']);
        } else {
            header('Location: gtransacciones');
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $idtransaccion = limpiar_datos($_POST['idtransaccion']);
        $tabselected = limpiar_datos($_POST["tabselected"]);
        $modtransaccion->id = $idtransaccion;
        $modtransaccion->valor = limpiar_datos($_POST['valor']);
        $modtransaccion->fecha = limpiar_datos($_POST['fecha']);
        $modtransaccion->nota = limpiar_datos($_POST['nota']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_modgeneral
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_modgeneral'])) {
    try {
        $conexion->beginTransaction();

        $modtransaccion->modifyGeneral($conexion);

        $conexion->commit();

        $success_display = 'show';
        $success_text = 'La transaccion ha sido modificada.';

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_modgeneral
#region sub_modcategorias
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_modcategorias'])) {
    try {
        $conexion->beginTransaction();

        $selcategs = $_POST['categorias'];

        $modtransaccion->getListCategorias($selcategs);
        $modtransaccion->modifyCategorias($conexion);

        $conexion->commit();

        $success_display = 'show';
        $success_text = 'La transaccion ha sido modificada.';

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_modcategorias
#region try
try {
    $categs = CategoriaTransaccion::getList($conexion);
    $modtransaccion = Transaccion::get($idtransaccion, $conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/etransaccion.view.php';

?>

