<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$mod_config = new Config();
$funds = 0;
$commission = 0;
$adjusted_funds = 0;
$preliminary_send_amount = 0;
$paypal_fee = 0;
$send_amount = 0;
$calculation_performed = false;

/**
 * Calculate commission based on funds and TRK fee percentage
 * @param float $funds The total funds entered by user
 * @param float $trkFeePercentage The TRK fee as a percentage (e.g., 15 for 15%)
 * @return float The calculated commission
 */
function calculateCommission($funds, $trkFeePercentage) {
    return $funds * ($trkFeePercentage / 100);
}

/**
 * Calculate preliminary send amount using the specified formula with adjusted funds
 * @param float $adjustedFunds The adjusted funds amount (original funds - commission)
 * @return float The preliminary calculated send amount (before PayPal fee cap)
 */
function calculatePreliminarySendAmount($adjustedFunds) {
    // Work in cents to avoid floating point issues
    $t = (int) round($adjustedFunds * 100);

    // Formula: send = floor((20/21) * t)
    $s = (int) floor((20 / 21) * $t);

    // Convert back to dollars
    return $s / 100.0;
}

/**
 * Calculate PayPal fee with $4.99 cap
 * @param float $adjustedFunds The adjusted funds amount
 * @param float $preliminarySendAmount The preliminary send amount before cap
 * @return float The PayPal fee (capped at $4.99)
 */
function calculateCappedPaypalFee($adjustedFunds, $preliminarySendAmount) {
    $uncappedFee = $adjustedFunds - $preliminarySendAmount;
    return min($uncappedFee, 4.99);
}

/**
 * Calculate final send amount with PayPal fee cap applied
 * @param float $adjustedFunds The adjusted funds amount
 * @param float $cappedPaypalFee The capped PayPal fee
 * @return float The final send amount
 */
function calculateFinalSendAmount($adjustedFunds, $cappedPaypalFee) {
    return $adjustedFunds - $cappedPaypalFee;
}

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		// Initialize default values
		$funds = 0;
		$send_amount = 0;
		$calculation_performed = false;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get

#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$funds = (float) limpiar_datos($_POST['funds']);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion postsolo

#region sub_calcular
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_calcular'])) {
	try {
		// Validate input
		if (empty($funds) || $funds <= 0) {
			throw new Exception('Please enter a valid funds amount greater than 0.');
		}

		// Get configuration to access TRK fee
		$mod_config = Config::get($conexion);

		// Perform new calculation flow with PayPal fee cap
		// 1. Calculate Commission = funds × TRK fee percentage
		$commission = calculateCommission($funds, $mod_config->trk_fee);

		// 2. Calculate adjusted funds = original funds - Commission
		$adjusted_funds = $funds - $commission;

		// 3. Calculate preliminary send amount using adjusted funds
		$preliminary_send_amount = calculatePreliminarySendAmount($adjusted_funds);

		// 4. Calculate PayPal fee with $4.99 cap
		$paypal_fee = calculateCappedPaypalFee($adjusted_funds, $preliminary_send_amount);

		// 5. Calculate final send amount with capped PayPal fee
		$send_amount = calculateFinalSendAmount($adjusted_funds, $paypal_fee);

		$calculation_performed = true;

		$success_display = 'show';
		$success_text = 'Calculation completed successfully.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_calcular

#region try
try {
	// Get configuration data including trk_fee
	$mod_config = Config::get($conexion);
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/gtrk_transfer.view.php';

?>
