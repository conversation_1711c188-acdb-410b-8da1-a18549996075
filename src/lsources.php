<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/source.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_addsource_agregar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addsource_agregar'])) {
    try {
        $new_source         = new Source();
        $new_source->nombre = limpiar_datos($_POST['addsource_nombre']);
        $new_source->agregar($conexion);

        $success_display = 'show';
        $success_text = 'El source ha sido agregado.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addsource_agregar
#region try
try {
    $sources = Source::get_list(array(), $conexion);
    
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lsources.view.php';

?>
