<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/objective.php';
require_once __ROOT__ . '/src/general/preparar.php';

$selectedtipo = '';
$selectedboss = '';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $newobjective = new Objective;
        $newobjective->times = 1;

        if (isset($_SESSION['selectedtipo'])) {
            $selectedtipo = $_SESSION['selectedtipo'];
            $selectedboss = $_SESSION['selectedboss'];

            unset($_SESSION['selectedtipo']);
            unset($_SESSION['selectedboss']);
        } else {
            header('Location: lobjectives');
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $selectedtipo = limpiar_datos($_POST['selectedtipo']);
        $selectedboss = limpiar_datos($_POST['selectedboss']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_save
if ($_SERVER['REQUEST_METHOD'] == 'POST' && (isset($_POST['sub_save']) || isset($_POST['sub_saveexit']))) {
    try {
        $conexion->beginTransaction();

        $newobjective = new Objective;
        $newobjective->module = limpiar_datos($_POST['module']);
        $newobjective->description = limpiar_datos($_POST['description']);
        $newobjective->note = limpiar_datos($_POST['note']);
        $newobjective->solicitante = limpiar_datos($_POST['solicitante']);
        $newobjective->proyecto = limpiar_datos($_POST['proyecto']);
        $newobjective->priority = @limpiar_datos($_POST['priority']);
        $newobjective->times = 1;
        $newobjective->pinned = @getvalue_checkbox($_POST['pinned']);
        $newobjective->add($conexion);

        $conexion->commit();

        if (isset($_POST['sub_saveexit'])) {
            if($selectedtipo == 'group' && $selectedboss == 'group'){
                header('Location: lgroupobjectives');
            } else{
                $_SESSION['selectedtipo'] = $selectedtipo;
                $_SESSION['selectedboss'] = $selectedboss;

                header('Location: lobjectives');
            }
        }
    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_back
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_back'])) {
    try {
        if($selectedtipo == 'group' && $selectedboss == 'group'){
            header('Location: lgroupobjectives');
        } else{
            $_SESSION['selectedtipo'] = $selectedtipo;
            $_SESSION['selectedboss'] = $selectedboss;

            header('Location: lobjectives');
        }
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region try
try {
    $proyectos = Objective::get_listproyectos($conexion);
    $solicitantes = Objective::get_listsolicitantes($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion

require_once __ROOT__ . '/views/iobjective.view.php';

?>