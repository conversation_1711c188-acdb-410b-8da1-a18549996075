<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/compra.php';
require_once __ROOT__ . '/src/classes/inventariocategoria.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['d'])) {
            $success_display = 'show';
            $success_text = 'La compra ha sido eliminada.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $newcompra = new Compra();
        $newcompra->inventariocategoria = new InventarioCategoria();
        $newcompra->inventariocategoria->id = limpiar_datos($_POST['idinventariocategoria']);
        $newcompra->nombre = limpiar_datos($_POST['nombre']);
        $newcompra->add($conexion);

        $newcompra = new Compra();

        $success_display = 'show';
        $success_text = 'La compra ha sido agregada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add
#region sub_editcompra
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editcompra'])) {
    try {
        $_SESSION['idcompra'] = limpiar_datos($_POST['sidcompra']);

        header('Location: ecompra');

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editcompra
#region try
try {
    $categorias = InventarioCategoria::getList($conexion);
    $compras = Compra::getListAll($conexion);

    $param = array();
    $param['includetrk'] = 1;
    $budgets = Budget::getList($param, $conexion);

    $disponible = Budget::getSumValor($budgets);

    $param = array();
    $param['disponible'] = $disponible;
    $param['dias'] = 7;

    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);

    $param['dias'] = 15;

    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);

    $param['dias'] = 30;

    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try


require_once __ROOT__ . '/views/lcompras.view.php';

?>

