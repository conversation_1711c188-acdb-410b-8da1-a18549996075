<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/bingsearch.php';
require_once __ROOT__ . '/src/general/preparar.php';

$bingsearches = array();
$opentab = 0;
$bingsearchurl = '';

#region sub_addsearch
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addsearch'])) {
    try {
        $search = limpiar_datos($_POST['search']);

        $newbingsearch = new BingSearch();
        $newbingsearch->search = $search;
        $newbingsearch->add($conexion);

        $search = '';

        $success_display = 'show';
        $success_text = 'La busqueda ha sido agregada.';

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addsearch
#region sub_delbingsearch
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delbingsearch'])) {
    try {
        $del_id_bingsearch = limpiar_datos($_POST['mdldelbingsearch_id_bingsearch']);

        BingSearch::delete($del_id_bingsearch, $conexion);

        $success_display = 'show';
        $success_text = 'Busqueda eliminada.';
				
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delbingsearch
#region sub_selrandom
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_selrandom'])) {
    try {
        $selbingsearch = BingSearch::getrandom($conexion);

        if (!empty(ordena($selbingsearch->id_bingsearch))) {
            $opentab = 1;
            $bingsearchurl = $selbingsearch->searchcomplete;
        }
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_selrandom
#region try
try {
    $bingsearches = BingSearch::get_list(0,$conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lbingsearches.view.php';

?>