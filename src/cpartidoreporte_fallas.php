<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoapuestaanalisis.php';
require_once __ROOT__ . '/src/classes/partidoapuestarazon.php';
require_once __ROOT__ . '/src/general/preparar.php';

$fecharange = '';
$max_perc = 90;
$min_perc = 40;
$reporte = array();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {


    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_search
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search'])) {
    try {
        $fecharange = limpiar_datos($_POST['fecharange']);

        //validaciones
        validar_textovacio($fecharange, 'Debe especificar el rango de fecha');

        //razones perdidas de los partidos dentro de la fecha seleccionada.
        $param = array();
        $param['fecharange']  = $fecharange;
        $param['group_razon'] = 1;
        $razones_perdida = PartidoApuestaRazon::get_list($param, $conexion);

        //construir reporte:
        $reporte = array();

        get_criterios($reporte,$razones_perdida, $max_perc,$min_perc);

        //ganadas
        $n = 0;

        get_data($n, $reporte,1,0,'home',$fecharange,$max_perc,$min_perc, $conexion);
        get_data($n, $reporte,1,0,'away',$fecharange,$max_perc,$min_perc, $conexion);
        get_data_both($n,$reporte,1,0,$fecharange,$conexion);
        get_data_avg_both($n,$reporte,1,0,$fecharange,$max_perc,$min_perc,$conexion);
        get_data_avg($n,$reporte,1,0,1,0,$fecharange,$max_perc,$min_perc,$conexion);
        get_data_avg($n,$reporte,1,0,0,1,$fecharange,$max_perc,$min_perc,$conexion);
        get_data_razones($n,$reporte,1,0,$razones_perdida,$fecharange,$conexion);

        //perdidas
        $n = 0;

        get_data($n, $reporte,0,1,'home',$fecharange,$max_perc,$min_perc, $conexion);
        get_data($n, $reporte,0,1,'away',$fecharange,$max_perc,$min_perc, $conexion);
        get_data_both($n,$reporte,0,1,$fecharange,$conexion);
        get_data_avg_both($n,$reporte,0,1,$fecharange,$max_perc,$min_perc,$conexion);
        get_data_avg($n,$reporte,0,1,1,0,$fecharange,$max_perc,$min_perc,$conexion);
        get_data_avg($n,$reporte,0,1,0,1,$fecharange,$max_perc,$min_perc,$conexion);
        get_data_razones($n,$reporte,0,1,$razones_perdida,$fecharange,$conexion);

        get_diff($reporte);
        get_bgcolor($reporte);

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_search
#region sub_update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_update'])) {
    try {


	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_update
#region try
try {


} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try
#region region get criterios
function get_criterios(&$reporte, array $razones_perdida, $max_perc, $min_perc): void
{
    $n = 0;

    #region region home
    for ($i = $max_perc; $i >= $min_perc; $i -= 10) {
        $reporte[$n++]['criterio'] = 'Ult. partidos Home: ~' . $i . '%';
    }
    $reporte[$n++]['criterio'] = 'Ult. partidos Home: 1ro partido mas reciente';
    $reporte[$n++]['criterio'] = 'Ult. partidos Home: 2do partido mas reciente';
    $reporte[$n++]['criterio'] = 'Ult. partidos Home: 3ro partido mas reciente';
    $reporte[$n++]['criterio'] = 'Ult. partidos Home: mitad';
    $reporte[$n++]['criterio'] = 'Ult. partidos Home: resto';
    #endregion home

    #region region away
    for ($i = $max_perc; $i >= $min_perc; $i -= 10) {
        $reporte[$n++]['criterio'] = 'Ult. partidos Away: ~' . $i . '%';
    }
    $reporte[$n++]['criterio'] = 'Ult. partidos Away: 1ro partido mas reciente';
    $reporte[$n++]['criterio'] = 'Ult. partidos Away: 2do partido mas reciente';
    $reporte[$n++]['criterio'] = 'Ult. partidos Away: 3ro partido mas reciente';
    $reporte[$n++]['criterio'] = 'Ult. partidos Away: mitad';
    $reporte[$n++]['criterio'] = 'Ult. partidos Away: resto';
    #endregion away

    #region region both
    $reporte[$n++]['criterio'] = 'Ult. partidos Both/Same fixture';
    #endregion both

    #region region avg both
    for ($i = $max_perc; $i >= $min_perc; $i -= 10) {
        $reporte[$n++]['criterio'] = 'Ult. partidos AVG Home & Away: ~' . $i . '%';
    }
    #endregion avg both

    #region region avg
    for ($i = $max_perc; $i >= $min_perc; $i -= 10) {
        $reporte[$n++]['criterio'] = 'AVG ALL: ~' . $i . '%';
    }
    for ($i = $max_perc; $i >= $min_perc; $i -= 10) {
        $reporte[$n++]['criterio'] = 'AVG SPECIFIC: ~' . $i . '%';
    }
    #endregion avg

    #region region razones perdida
    /** @var PartidoApuestaRazon[] $razones_perdida */
    foreach ($razones_perdida as $razon_perdida) {
        $reporte[$n++]['criterio'] = 'RAZON PERDIDA: ' . $razon_perdida->nombre_razon_perdida;
    }
    #endregion razones perdida
}
#endregion get criterios
#region region get data
/**
 * @throws Exception
 */
function get_data(&$n, array &$reporte, $solo_ganado, $solo_perdido, $equipo, $fecharange, $max_perc, $min_perc, PDO $conexion): void
{
    if ($solo_ganado == 1 && $solo_perdido == 1) {
        throw new Exception('No se pueden mostrar ganados y perdidos al mismo tiempo');
    }

    $param = array();
    $param['fecharange'] = $fecharange;
    $param['equipo'] = $equipo;
    $param['solo_ganado'] = $solo_ganado;
    $param['solo_perdido'] = $solo_perdido;

    $nom_column = '';

    if ($solo_ganado == 1) {
        $nom_column = 'wins';
    }
    if ($solo_perdido == 1) {
        $nom_column = 'losses';
    }

    for ($i = $max_perc; $i >= $min_perc; $i -= 10) {
        $param['perc_ult_partidos'] = $i;

        $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);
    }

    $param['perc_ult_partidos'] = 0; //resetear

    $param['puesto'] = '1ro';
    $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);

    $param['puesto'] = '2do';
    $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);

    $param['puesto'] = '3ro';
    $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);

    $param['puesto'] = 'mitad';
    $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);

    $param['puesto'] = 'resto';
    $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);
}
#endregion get data
#region region get data both
/**
 * @throws Exception
 */
function get_data_both(&$n, &$reporte, $solo_ganado, $solo_perdido, $fecharange, $conexion): void
{
    if($solo_ganado == 1 && $solo_perdido == 1){
        throw new Exception('No se pueden mostrar ganados y perdidos al mismo tiempo');
    }

    $param = array();
    $param['fecharange']   = $fecharange;
    $param['solo_ganado']  = $solo_ganado;
    $param['solo_perdido'] = $solo_perdido;
    $param['solo_both']    = 1;

    $nom_column = '';

    if($solo_ganado == 1){
        $nom_column = 'wins';
    }
    if($solo_perdido == 1){
        $nom_column = 'losses';
    }

    $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);
}
#endregion get data both
#region region data avg both
/**
 * @throws Exception
 */
function get_data_avg_both(&$n, &$reporte, $solo_ganado, $solo_perdido, $fecharange, $max_perc, $min_perc, PDO $conexion): void
{
    if($solo_ganado == 1 && $solo_perdido == 1){
        throw new Exception('No se pueden mostrar ganados y perdidos al mismo tiempo');
    }

    $param = array();
    $param['fecharange']   = $fecharange;
    $param['solo_ganado']  = $solo_ganado;
    $param['solo_perdido'] = $solo_perdido;

    $nom_column = '';

    if($solo_ganado == 1){
        $nom_column = 'wins';
    }
    if($solo_perdido == 1){
        $nom_column = 'losses';
    }

    for ($i = $max_perc; $i >= $min_perc; $i -= 10) {
        $param['perc_ult_partidos_avg_both'] = $i;

        $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);
    }
}
#endregion data avg both
#region region get data avg
/**
 * @throws Exception
 */
function get_data_avg(&$n, &$reporte, $solo_ganado, $solo_perdido, $solo_avg_all, $solo_avg_specific, $fecharange, $max_perc, $min_perc, PDO $conexion): void
{
    if($solo_ganado == 1 && $solo_perdido == 1){
        throw new Exception('No se pueden mostrar ganados y perdidos al mismo tiempo');
    }
    if($solo_avg_all == 1 && $solo_avg_specific == 1){
        throw new Exception('No se pueden mostrar ambos avg al mismo tiempo');
    }

    $param = array();
    $param['fecharange']        = $fecharange;
    $param['solo_avg_all']      = $solo_avg_all;
    $param['solo_avg_specific'] = $solo_avg_specific;
    $param['solo_ganado']       = $solo_ganado;
    $param['solo_perdido']      = $solo_perdido;

    $nom_column = '';

    if($solo_ganado == 1){
        $nom_column = 'wins';
    }
    if($solo_perdido == 1){
        $nom_column = 'losses';
    }

    for ($i = $max_perc; $i >= $min_perc; $i -= 10) {
        $param['perc_avg'] = $i;
        $reporte[$n++][$nom_column] = PartidoApuestaAnalisis::get_count($param, $conexion);
    }
}
#endregion get data avg
#region region get data razones
/**
 * @throws Exception
 */
function get_data_razones(&$n, &$reporte, $solo_ganado, $solo_perdido, $razones_perdida, $fecharange, PDO $conexion): void
{
    $param = array();
    $param['fecharange']   = $fecharange;
    $param['solo_ganado']  = $solo_ganado;
    $param['solo_perdido'] = $solo_perdido;

    $nom_column = '';

    if($solo_ganado == 1){
        $nom_column = 'wins';
    }
    if($solo_perdido == 1){
        $nom_column = 'losses';
    }

    /** @var PartidoApuestaRazon[] $razones_perdida */
    foreach ($razones_perdida as $razon_perdida) {
        $param['nombre_razon'] = $razon_perdida->nombre_razon_perdida;
        $reporte[$n++][$nom_column] = PartidoApuestaRazon::get_count($param, $conexion);
    }
}
#endregion get data razones
#region region get diff
function get_diff(array &$reporte): void
{
    for ($i = 0; $i < count($reporte); $i++) {
        $reporte[$i]['diff'] = $reporte[$i]['wins'] - $reporte[$i]['losses'];
    }
}
#endregion get diff
#region region get bgcolor
function get_bgcolor(array &$reporte): void
{
    for ($i = 0; $i < count($reporte); $i++) {
        if($reporte[$i]['diff'] < 0){
            $reporte[$i]['bgcolor'] = 'bg-red-700';

        } elseif($reporte[$i]['diff'] == 0|| $reporte[$i]['diff'] == 1){
            $reporte[$i]['bgcolor'] = 'bg-yellow-700';
        } else{
            $reporte[$i]['bgcolor'] = '';
        }
    }
}
#endregion get bgcolor

require_once __ROOT__ . '/views/cpartidoreporte_fallas.view.php';

?>