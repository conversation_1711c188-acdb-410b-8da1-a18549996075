<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/project.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region init variables
$sel_id_project = '';
#endregion init variables
#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['i'])) {
            $success_display = 'show';
            $success_text = 'Project added.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_edit_project
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_edit_project'])) {
    try {
        $_SESSION['id_project'] = limpiar_datos($_POST['sel_id_project']);

        header('Location: eproject');

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_edit_project
#region try
try {
    $projects = Project::getList($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lprojects.view.php';

?>








