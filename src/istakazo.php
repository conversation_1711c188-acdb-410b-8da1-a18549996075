<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/source.php';
require_once __ROOT__ . '/src/classes/stakazo.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$apuesta     = 0;
$new_stakazo = new Stakazo();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $new_stakazo->costo = limpiar_datos($_POST['costo']);
        $new_stakazo->cuota = limpiar_datos($_POST['cuota']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region try
try {
    $sources    = Source::get_list(array(), $conexion);
    $cur_config = Config::get($conexion);

    if($new_stakazo->costo > 0 && $new_stakazo->cuota > 0){
        $new_stakazo->bank            = $cur_config->bt_bank;
        $new_stakazo->min_porc_profit = $cur_config->bt_min_porc_profit;
        $new_stakazo->min_porc_bank   = $cur_config->bt_min_porc_bank;
        $new_stakazo->calcular();
    }
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/istakazo.view.php';

?>