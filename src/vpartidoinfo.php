<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/classes/partidotorneo.php';
require_once __ROOT__ . '/src/classes/partidoporrevisar.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/classes/tipster.php';
require_once __ROOT__ . '/src/classes/paisseason.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region region initiate variables
/** @var Config $current_config */

$idpartido   = '';
$tabselected = 6; // tab total goles by default
$sum_riesgo  = 0;
$totalcorners_masde   = PartidoInfo::DEFAULT_TOTALCORNERS_MASDE;
$totalcorners_menosde = PartidoInfo::DEFAULT_TOTALCORNERS_MENOSDE;
$cornershome_masde    = PartidoInfo::DEFAULT_CORNERSHOME_MASDE;
$cornershome_menosde  = PartidoInfo::DEFAULT_CORNERSHOME_MENOSDE;
$cornersaway_masde    = PartidoInfo::DEFAULT_CORNERSAWAY_MASDE;
$cornersaway_menosde  = PartidoInfo::DEFAULT_CORNERSAWAY_MENOSDE;
$totalgoles_masde     = PartidoInfo::DEFAULT_TOTALGOLES_MASDE;
$totalgoles_menosde   = PartidoInfo::DEFAULT_TOTALGOLES_MENOSDE;
$goleshome_masde      = PartidoInfo::DEFAULT_GOLES_HOME_MASDE;
$goleshome_menosde    = PartidoInfo::DEFAULT_GOLES_HOME_MENOSDE;
$golesaway_masde      = PartidoInfo::DEFAULT_GOLES_AWAY_MASDE;
$golesaway_menosde    = PartidoInfo::DEFAULT_GOLES_AWAY_MENOSDE;
$partidotorneos      = array();
$rank_corners_athome = array();
$rank_corners_ataway = array();
$infogolesaway       = array();
$info_hvsa           = array();
$infobtts            = array();
$tableprobs          = array();
$apuestastipos       = array();
$infototalgoles      = array();
$infogoleshome       = array();
$infocornershome     = array();
$infocornersaway     = array();
$numeropartidos      = array();
$nrank_corners_athome = 1;
$nrank_corners_ataway = 1;
$valorapuesta_apostar = '';
$potencial_apostar = '';
$riesgo_apostar = '';
$apuestatipo_apostar = '';
$tipster_apostar = '';
$count_apuestas = 0;
$idpartidoordenado = '';
$modpartido = new Partido;
$infototalcorners = array();
$current_config = new Config;

const HOME_ROW = 1;
const AWAY_ROW = 5;
#endregion initiate variables

#region try
try {
    $current_config = Config::get($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try
#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        if (isset($_SESSION['idpartido'])) {
            $idpartido = $_SESSION['idpartido'];
        
            // logic:
            $idpartidoordenado = ordena($idpartido);

            if(isset($_SESSION["loaded_$idpartidoordenado"])){
                $tabselected = $_SESSION["tabselected_$idpartidoordenado"];

                $totalcorners_masde = $_SESSION["totalcorners_masde_$idpartidoordenado"];
                $totalcorners_menosde = $_SESSION["totalcorners_menosde_$idpartidoordenado"];
                $cornershome_masde = $_SESSION["cornershome_masde_$idpartidoordenado"];
                $cornershome_menosde = $_SESSION["cornershome_menosde_$idpartidoordenado"];
                $cornersaway_masde = $_SESSION["cornersaway_masde_$idpartidoordenado"];
                $cornersaway_menosde = $_SESSION["cornersaway_menosde_$idpartidoordenado"];
                $totalgoles_masde = $_SESSION["totalgoles_masde_$idpartidoordenado"];
                $totalgoles_menosde = $_SESSION["totalgoles_menosde_$idpartidoordenado"];
                $goleshome_masde = $_SESSION["goleshome_masde_$idpartidoordenado"];
                $goleshome_menosde = $_SESSION["goleshome_menosde_$idpartidoordenado"];
                $golesaway_masde = $_SESSION["golesaway_masde_$idpartidoordenado"];
                $golesaway_menosde = $_SESSION["golesaway_menosde_$idpartidoordenado"];
            }

            if (isset($_SESSION['tabselected_ipartido'])){
                $tabselected = $_SESSION['tabselected_ipartido'];

                unset($_SESSION['tabselected_ipartido']);
            }
        
            unset($_SESSION['idpartido']);
        } else {
            header('Location: lpartidos');
        }

        if (isset($_GET['i'])) {
            $success_display = 'show';
            $success_text = 'La apuesta ha sido ingresada.';
        }
        if (isset($_GET['im'])) {
            $success_display = 'show';
            $success_text = 'La apuesta ha sido marcada.';
        }

        $actconfig = Config::get($conexion);
        $riesgo_apostar = $actconfig->valmin_prediccion;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $idpartido = limpiar_datos($_POST['idpartido']);
        $tabselected = limpiar_datos($_POST["tabselected"]);
        $totalcorners_masde = limpiar_datos($_POST["totalcorners_masde"]);
        $totalcorners_menosde = limpiar_datos($_POST["totalcorners_menosde"]);
        $cornershome_masde = limpiar_datos($_POST["cornershome_masde"]);
        $cornershome_menosde = limpiar_datos($_POST["cornershome_menosde"]);
        $cornersaway_masde = limpiar_datos($_POST["cornersaway_masde"]);
        $cornersaway_menosde = limpiar_datos($_POST["cornersaway_menosde"]);
        $totalgoles_masde = limpiar_datos($_POST["totalgoles_masde"]);
        $totalgoles_menosde = limpiar_datos($_POST["totalgoles_menosde"]);
        $goleshome_masde = limpiar_datos($_POST["goleshome_masde"]);
        $goleshome_menosde = limpiar_datos($_POST["goleshome_menosde"]);
        $golesaway_masde = limpiar_datos($_POST["golesaway_masde"]);
        $golesaway_menosde = limpiar_datos($_POST["golesaway_menosde"]);
        $newtorneos = limpiar_datos($_POST["newtorneos"]);
        $apuestatipo_apostar = limpiar_datos($_POST["apuestatipo_apostar"]);
        $valorapuesta_apostar = limpiar_datos($_POST["valorapuesta_apostar"]);
        $riesgo_apostar = limpiar_datos($_POST["riesgo_apostar"]);
        $potencial_apostar = limpiar_datos($_POST["potencial_apostar"]);

        // guardar variables session de este partido en especifico.
        $idpartidoordenado = ordena($idpartido);

        $_SESSION["loaded_$idpartidoordenado"] = 1;
        $_SESSION["tabselected_$idpartidoordenado"] = $tabselected;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_apostar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar'])) {
    try {
        $method_sub_apostar = 1;

        validar_textovacio($apuestatipo_apostar, "Debe especificar el tipo de apuesta");
        validar_textovacio($valorapuesta_apostar, "Debe especificar el valor de la apuesta");

        $_SESSION['idpartido'] = $idpartido;

        $nomotrasapuestatipo = limpiar_datos($_POST['nomotrasapuestatipo']);
        $valorotraapuesta = limpiar_datos($_POST['valorotraapuesta']);

        $idapuestatipo = ApuestaTipo::getIdByNombre($nomotrasapuestatipo, $conexion);

        $_SESSION['idtipoapuesta'] = $idapuestatipo;
        $_SESSION['valorapuesta'] = $valorotraapuesta;

        header('Location: ipartidootrasapuestas');
        exit();         

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar
#region sub_apostarprediccion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostarprediccion'])) {
    try {
        $method_sub_apostarprediccion = 1;

        validar_textovacio($apuestatipo_apostar, "Debe especificar el tipo de apuesta");
        validar_textovacionotzero($valorapuesta_apostar, "Debe especificar el valor de la apuesta");
        validar_textovacio($riesgo_apostar, "Debe especificar el riesgo");
        validar_textovacio($potencial_apostar, "Debe especificar el potencial");

        $id_apuestatipo = ApuestaTipo::getIdByNombre($apuestatipo_apostar, $conexion);

        $newpartidoapuesta = new PartidoApuesta();
        $newpartidoapuesta->idpartido = $idpartido;
        $newpartidoapuesta->idapuestatipo = $id_apuestatipo;
        $newpartidoapuesta->valorapuesta = $valorapuesta_apostar;
        $newpartidoapuesta->riesgo = $riesgo_apostar;
        $newpartidoapuesta->potencial = $potencial_apostar;
        $newpartidoapuesta->porccrit1 = 0;
        $newpartidoapuesta->porccrit2 = 0;
        $newpartidoapuesta->porccrit3 = 0;
        $newpartidoapuesta->porccrit4 = 0;
        $newpartidoapuesta->porccrit5 = 0;
        $newpartidoapuesta->porccrit6 = 0;
        $newpartidoapuesta->porccrit7 = 0;
        $newpartidoapuesta->porccrit8 = 0;
        $newpartidoapuesta->porccrit9 = 0;
        $newpartidoapuesta->isprediccion = 1;
        $newpartidoapuesta->isprediccionpremium = 1;
        $newpartidoapuesta->tipster = '';
        $newpartidoapuesta->winprobabilityporc = 0;
        $newpartidoapuesta->nota = '';
        $newpartidoapuesta->betsizekellycritporc = 0;
        $newpartidoapuesta->estado = 1;
        $newpartidoapuesta->add($conexion);  

        $success_display = 'show';
        $success_text = 'La prediccion ha sido ingresada.';

        $apuestatipo_apostar = '';
        $valorapuesta_apostar = '';
        $potencial_apostar = '';
        $tipster_apostar = '';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostarprediccion
#region sub_addtorneo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addtorneo'])) {
    try {
        $method_sub_addtorneo = 1;
        
        $newpartidotorneo = new PartidoTorneo;
        $newpartidotorneo->partido = new Partido;
        $newpartidotorneo->partido->id = $idpartido;
        $newpartidotorneo->pais = new Pais;
        $newpartidotorneo->pais->nombre = $newtorneos;
        $newpartidotorneo->add($conexion);

        $success_display = 'show';
        $success_text = 'El torneo ha sido agregado.';
        
        $newtorneos = '';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addtorneo
#region sub_totalcorners_masde
for ($i=PartidoInfo::MIN_TOTAL_CORNERS; $i <= PartidoInfo::MAX_TOTAL_CORNERS; $i++) { 
    $method_sub_totalcorners_masde = 1;

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_totalcorners_masde_$i"])) {
        try {
            $totalcorners_masde = $i + 0.5;
    
        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_totalcorners_masde
#region sub_totalcorners_menosde
for ($i=PartidoInfo::MIN_TOTAL_CORNERS; $i <= PartidoInfo::MAX_TOTAL_CORNERS; $i++) { 
    $method_sub_totalcorners_menosde = 1;

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_totalcorners_menosde_$i"])) {
        try {
            $totalcorners_menosde = $i + 0.5;
    
        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_totalcorners_menosde
#region sub_cornershome_masde
for ($i=PartidoInfo::MIN_CORNERS_PERTEAM; $i <= PartidoInfo::MAX_CORNERS_PERTEAM; $i++) { 
    $method_sub_cornershome_masde = 1;

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_cornershome_masde_$i"])) {
        try {
            $cornershome_masde = $i + 0.5;
    
        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_cornershome_masde
#region sub_cornershome_menosde
for ($i=PartidoInfo::MIN_CORNERS_PERTEAM; $i <= PartidoInfo::MAX_CORNERS_PERTEAM; $i++) { 
    $method_sub_cornershome_menosde = 1;

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_cornershome_menosde_$i"])) {
        try {
            $cornershome_menosde = $i + 0.5;
    
        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_cornershome_menosde
#region sub_cornersaway_masde
for ($i=PartidoInfo::MIN_CORNERS_PERTEAM; $i <= PartidoInfo::MAX_CORNERS_PERTEAM; $i++) { 
    $method_sub_cornersaway_masde = 1;

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_cornersaway_masde_$i"])) {
        try {
            $cornersaway_masde = $i + 0.5;
    
        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_cornersaway_masde
#region sub_cornersaway_menosde
for ($i=PartidoInfo::MIN_CORNERS_PERTEAM; $i <= PartidoInfo::MAX_CORNERS_PERTEAM; $i++) { 
    $method_sub_cornersaway_menosde = 1;

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_cornersaway_menosde_$i"])) {
        try {
            $cornersaway_menosde = $i + 0.5;
    
        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_cornersaway_menosde
#region sub_totalgoles_masde
for ($i=PartidoInfo::MIN_TOTAL_GOLES; $i <= PartidoInfo::MAX_TOTAL_GOLES; $i++) {
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_totalgoles_masde_$i"])) {
        try {
            $totalgoles_masde = $i + 0.5;

        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_totalgoles_masde
#region sub_totalgoles_menosde
for ($i=PartidoInfo::MIN_TOTAL_GOLES; $i <= PartidoInfo::MAX_TOTAL_GOLES; $i++) {
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_totalgoles_menosde_$i"])) {
        try {
            $totalgoles_menosde = $i + 0.5;

        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_totalgoles_menosde
#region sub_goleshome_masde
for ($i=PartidoInfo::MIN_GOLES_PERTEAM; $i <= PartidoInfo::MAX_GOLES_PERTEAM; $i++) {
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_goleshome_masde_$i"])) {
        try {
            $goleshome_masde = $i + 0.5;

        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_goleshome_masde
#region sub_goleshome_menosde
for ($i=PartidoInfo::MIN_GOLES_PERTEAM; $i <= PartidoInfo::MAX_GOLES_PERTEAM; $i++) {
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_goleshome_menosde_$i"])) {
        try {
            $goleshome_menosde = $i + 0.5;

        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_goleshome_menosde
#region sub_golesaway_masde
for ($i=PartidoInfo::MIN_GOLES_PERTEAM; $i <= PartidoInfo::MAX_GOLES_PERTEAM; $i++) {
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_golesaway_masde_$i"])) {
        try {
            $golesaway_masde = $i + 0.5;

        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_golesaway_masde
#region sub_golesaway_menosde
for ($i=PartidoInfo::MIN_GOLES_PERTEAM; $i <= PartidoInfo::MAX_GOLES_PERTEAM; $i++) {
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["sub_golesaway_menosde_$i"])) {
        try {
            $golesaway_menosde = $i + 0.5;

        } catch (Exception $e) {
            $error_display = 'show';
            $error_text = $e->getMessage();
        }
    }
}
#endregion sub_golesaway_menosde
#region sub_proxpartidoporrevisar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_proxpartidoporrevisar'])) {
    try {
        $method_sub_proxpartidoporrevisar = 1;
        
        $respuesta = PartidoPorRevisar::getIdProximoPorRevisar($conexion);
        $_SESSION['idpartidoporrevisar'] = $respuesta['id'];

        if($respuesta['count'] > 0){
            header('Location: ipartido?newppr=1');
            exit();

        } else{
            header('Location: lpartidosporrevisar?nppr=1');
            exit();
        }

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_proxpartidoporrevisar
#region sub_editpartidoapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editpartidoapuesta'])) {
    try {
        $method_editpartidoapuesta = 1;

        $_SESSION['idpartidoapuesta'] = limpiar_datos($_POST['selidpartidoapuesta']);

        header('Location: epartidoapuesta');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editpartidoapuesta
#region sub_addtorneo_fromtable
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addtorneo_fromtable'])) {
    try {
        $method_sub_addtorneo_fromtable = 1;
        
        $selidpais = limpiar_datos($_POST['selidpais']);

        $newpartidotorneo = new PartidoTorneo;
        $newpartidotorneo->partido = new Partido;
        $newpartidotorneo->partido->id = $idpartido;
        $newpartidotorneo->pais = new Pais;
        $newpartidotorneo->pais->id = $selidpais;
        $newpartidotorneo->add($conexion);

        $success_display = 'show';
        $success_text = 'El torneo ha sido agregado.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addtorneo_fromtable
#region sub_delpartidotorneo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delpartidotorneo'])) {
    try {
        $method_delpartidotorneo = 1;

        $selidpartidotorneo = limpiar_datos($_POST['selidpartidotorneo']);
        
        PartidoTorneo::delete($selidpartidotorneo, $conexion);

        $success_display = 'show';
        $success_text = 'El torneo ha sido eliminado.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delpartidotorneo
#region sub_verodds
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_verodds'])) {
    try {
        $_SESSION['idpartido'] = $idpartido;

        header('Location: ipartido_odds');
        exit();

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_verodds
#region try
try {
    $method_try = 1;

    $modpartido               = Partido::get($idpartido, $conexion);
    $partidosapuestas         = PartidoApuesta::getListByIdPartido($idpartido, $conexion);
    $apuestastipos            = ApuestaTipo::getList(array(),$conexion);
    $paises                   = Pais::getList($conexion);
    $pais_season              = PaisSeason::get(array('nom_pais'=>$modpartido->pais,'season'=>getYearActual()),$conexion);
    $partidotorneos           = PartidoTorneo::getList($idpartido, $conexion);
    $respuesta                = PartidoPorRevisar::getIdProximoPorRevisar($conexion);
    $count_partidosporrevisar = $respuesta['count'];
    $home_maxfecha            = format_date2(PartidoInfo::get_fechamax_byteam($modpartido->home, 'home', $conexion));
    $away_maxfecha            = format_date2(PartidoInfo::get_fechamax_byteam($modpartido->away, 'away', $conexion));

    //obtener numero de partidos por revisar del mismo torneo.
    $param = array();
    $param['solotoplay'] = 1;
    $param['solo_notdone'] = 1;
    $param['nom_pais'] = $modpartido->pais;
    $count_partidos_mismotorneo_porrevisar = count(PartidoPorRevisar::getList($param, $conexion));

    //obtener torneos disponibles de ambos equipos
    $param = array();
    $param['home'] = $modpartido->home;
    $param['away'] = $modpartido->away;
    $partidotorneos_disponible = PartidoInfo::get_list_byhome_n_away($param, $conexion);

    //obtener riesgo total y numero de apuestas pendientes.
    $param = array();
    $param['sumriesgo'] = 1;
    $sum_riesgo = PartidoApuesta::getSum($param, $conexion);
    $param['sumriesgo'] = 0;
    $param['countall'] = 1;
    $count_apuestas = PartidoApuesta::getSum($param, $conexion);

    #region region probabilities
    $param = array();
    $param['modpartido']           = $modpartido;
    $param['partidotorneos']       = $partidotorneos;
    $param['totalcorners_masde']   = $totalcorners_masde;
    $param['totalcorners_menosde'] = $totalcorners_menosde;
    $param['cornershome_masde']    = $cornershome_masde;
    $param['cornershome_menosde']  = $cornershome_menosde;
    $param['cornersaway_masde']    = $cornersaway_masde;
    $param['cornersaway_menosde']  = $cornersaway_menosde;
    $param['totalgoles_masde']     = $totalgoles_masde;
    $param['totalgoles_menosde']   = $totalgoles_menosde;
    $param['goleshome_masde']      = $goleshome_masde;
    $param['goleshome_menosde']    = $goleshome_menosde;
    $param['golesaway_masde']      = $golesaway_masde;
    $param['golesaway_menosde']    = $golesaway_menosde;
    $tableprobs = PartidoInfo::create_table_probabilities($param, $conexion);
    $criterios = $tableprobs['criterios'];
    $criterios_hvsa = $tableprobs['criterios_hvsa'];
    $infototalcorners = $tableprobs['infototalcorners'];
    $infocornershome = $tableprobs['infocornershome'];
    $infocornersaway = $tableprobs['infocornersaway'];
    $promediocorners = $tableprobs['promediocorners'];
    $numeropartidos = $tableprobs['numeropartidos'];
    $promediogoles = $tableprobs['promediogoles'];
    $infototalgoles = $tableprobs['infototalgoles'];
    $infogoleshome = $tableprobs['info_goleshome'];
    $infogolesaway = $tableprobs['info_golesaway'];
    #endregion probabilities

    //rankings
    $posession = $tableprobs['posession'];
    $shots = $tableprobs['shots'];
    $shotstarget = $tableprobs['shotstarget'];
    $corners = $tableprobs['corners'];
    $rank_goals = $tableprobs['rank_goals'];

    //Guardar variables de session para la proxima vez que se cargue la pagina:
    $_SESSION["totalcorners_masde_$idpartidoordenado"] = $totalcorners_masde;
    $_SESSION["totalcorners_menosde_$idpartidoordenado"] = $totalcorners_menosde;
    $_SESSION["cornershome_masde_$idpartidoordenado"] = $cornershome_masde;
    $_SESSION["cornershome_menosde_$idpartidoordenado"] = $cornershome_menosde;
    $_SESSION["cornersaway_masde_$idpartidoordenado"] = $cornersaway_masde;
    $_SESSION["cornersaway_menosde_$idpartidoordenado"] = $cornersaway_menosde;
    $_SESSION["totalgoles_masde_$idpartidoordenado"] = $totalgoles_masde;
    $_SESSION["totalgoles_menosde_$idpartidoordenado"] = $totalgoles_menosde;
    $_SESSION["goleshome_masde_$idpartidoordenado"] = $goleshome_masde;
    $_SESSION["goleshome_menosde_$idpartidoordenado"] = $goleshome_menosde;
    $_SESSION["golesaway_masde_$idpartidoordenado"] = $golesaway_masde;
    $_SESSION["golesaway_menosde_$idpartidoordenado"] = $golesaway_menosde;

    #region region criterios fixture
    $param_f = array();
    $param_f['modpartido'] = $modpartido;
    $fixture = crear_table_fixture($param_f);
    #endregion criterios fixture

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

// NOTA IMPORTANTE: estos posts estan despues del try ya que necesito que se cargue la informacion 
// de todas las tablas para poder transferirlas a otra pagina.

#region sub_apostar_totalcorners_masde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_totalcorners_masde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_MASXTOTALCORNERS);
        $_SESSION['valorapuesta']       = $totalcorners_masde;
        $_SESSION['winprobabilityporc'] = $infototalcorners['probabilidades']['promedio']['masde'];

        header('Location: ipartidootrasapuestas');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_totalcorners_masde
#region sub_apostar_totalcorners_menosde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_totalcorners_menosde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_MENOSXTOTALCORNERS);
        $_SESSION['valorapuesta']       = $totalcorners_menosde;
        $_SESSION['winprobabilityporc'] = $infototalcorners['probabilidades']['promedio']['menosde'];

        header('Location: ipartidootrasapuestas');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_totalcorners_menosde
#region sub_apostar_cornershome_masde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_cornershome_masde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_MASXCORNERSHOME);
        $_SESSION['valorapuesta']       = $cornershome_masde;
        $_SESSION['winprobabilityporc'] = $infocornershome['probabilidades']['promedio']['masde'];

        header('Location: ipartidootrasapuestas');
        exit();        

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_cornershome_masde
#region sub_apostar_cornershome_menosde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_cornershome_menosde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_MENOSXCORNERSHOME);
        $_SESSION['valorapuesta']       = $cornershome_menosde;
        $_SESSION['winprobabilityporc'] = $infocornershome['probabilidades']['promedio']['menosde'];

        header('Location: ipartidootrasapuestas');
        exit();        

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_cornershome_menosde
#region sub_apostar_cornersaway_masde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_cornersaway_masde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_MASXCORNERSAWAY);
        $_SESSION['valorapuesta']       = $cornersaway_masde;
        $_SESSION['winprobabilityporc'] = $infocornersaway['probabilidades']['promedio']['masde'];

        header('Location: ipartidootrasapuestas');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_cornersaway_masde
#region sub_apostar_cornersaway_menosde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_cornersaway_menosde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_MENOSXCORNERSAWAY);
        $_SESSION['valorapuesta']       = $cornersaway_menosde;
        $_SESSION['winprobabilityporc'] = $infocornersaway['probabilidades']['promedio']['menosde'];

        header('Location: ipartidootrasapuestas');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_cornersaway_menosde
#region sub_apostar_totalgoles_masde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_totalgoles_masde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_TOTAL_GOLES_MASDE);
        $_SESSION['valorapuesta']       = $totalgoles_masde;
        $_SESSION['winprobabilityporc'] = $infototalgoles['probabilidades']['promedio']['masde'];

        header('Location: ipartidootrasapuestas');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_totalgoles_masde
#region sub_apostar_totalgoles_menosde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_totalgoles_menosde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_TOTAL_GOLES_MENOSDE);
        $_SESSION['valorapuesta']       = $totalgoles_menosde;
        $_SESSION['winprobabilityporc'] = $infototalgoles['probabilidades']['promedio']['menosde'];

        header('Location: ipartidootrasapuestas');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_totalgoles_menosde
#region sub_apostar_goleshome_masde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_goleshome_masde'])) {
    try {
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_GOLES_HOME_MASDE);
        $_SESSION['valorapuesta'] = $goleshome_masde;
        $_SESSION['winprobabilityporc'] = $infogoleshome['probabilidades']['promedio']['masde'];

        header('Location: ipartidootrasapuestas');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_goleshome_masde
#region sub_apostar_goleshome_menosde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_goleshome_menosde'])) {
    try {
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_GOLES_HOME_MENOSDE);
        $_SESSION['valorapuesta'] = $goleshome_menosde;
        $_SESSION['winprobabilityporc'] = $infogoleshome['probabilidades']['promedio']['menosde'];

        header('Location: ipartidootrasapuestas');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_goleshome_menosde
#region sub_apostar_golesaway_masde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_golesaway_masde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_GOLES_AWAY_MASDE);
        $_SESSION['valorapuesta']       = $golesaway_masde;
        $_SESSION['winprobabilityporc'] = $infogolesaway['probabilidades']['promedio']['masde'];

        header('Location: ipartidootrasapuestas');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_golesaway_masde
#region sub_apostar_golesaway_menosde
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar_golesaway_menosde'])) {
    try {
        $_SESSION['idpartido']          = $idpartido;
        $_SESSION['idtipoapuesta']      = desordena(ApuestaTipo::ID_GOLES_AWAY_MENOSDE);
        $_SESSION['valorapuesta']       = $golesaway_menosde;
        $_SESSION['winprobabilityporc'] = $infogolesaway['probabilidades']['promedio']['menosde'];

        header('Location: ipartidootrasapuestas');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar_golesaway_menosde

/**
 * @throws Exception
 */
function crear_table_fixture($paramref): array
{
    try {
        /** @var Partido $modpartido */

        #region region parametros
        $modpartido = $paramref['modpartido'];
        #endregion parametros

        $fixture = array();
        $n       = 0;

        $fixture[$n]['criterio'] = 'xG';
        $fixture[$n]['home'] = $modpartido->home_xg;
        $fixture[$n]['away'] = $modpartido->away_xg;

        $n++;

        return $fixture;

    } catch (Exception $e) {
        throw new Exception($e->getMessage());
    }
}

require_once __ROOT__ . '/views/vpartidoinfo.view.php';

?>




