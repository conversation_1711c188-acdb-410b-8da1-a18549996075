<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$arriesgar = 0;
$i_array_pvac = 0;
$i_array_pvac2 = 0;
$i_array_p = 0;
$riesgo_diario = 0;
$orderby_winprobabilityporc = 0;
$sum_riesgo_ajustado = 0;
$focus_n_row = 0;
$guardar_solo = 0;
$n_row = 1;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        $actconfig = Config::get($conexion);
        $riesgo_diario = $actconfig->riesgo_diario;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $riesgo_diario = limpiar_datos($_POST['riesgo_diario']);
        $orderby_winprobabilityporc = @getvalue_checkbox($_POST['orderby_winprobabilityporc']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_modify
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_modify'])) {
    try {
        $method_sub_modify = 1;
        
        $mdlidpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);
        $mdlriesgo           = limpiar_datos($_POST['mdl_checkapuesta_riesgo']);
        $mdlpotencial        = limpiar_datos($_POST['mdl_checkapuesta_potencial']);
        $mdlcupon            = limpiar_datos($_POST['mdl_checkapuesta_cupon']);
        $mdln_row            = limpiar_datos($_POST['mdl_checkapuesta_n_row']);

        $modpartidoapuesta = new PartidoApuesta;
        $modpartidoapuesta->id        = $mdlidpartidoapuesta;
        $modpartidoapuesta->riesgo    = $mdlriesgo;
        $modpartidoapuesta->potencial = $mdlpotencial;
        $modpartidoapuesta->id_cupon  = $mdlcupon;
        $modpartidoapuesta->modify_quick($conexion);

        $focus_n_row = $mdln_row - 1;

        $success_display = 'show';
        $success_text = 'La apuesta ha sido modificada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_modify
#region sub_delpartidoapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delpartidoapuesta'])) {
    try {
        $method_sub_delpartidoapuesta = 1;
        
        $mdlidpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);
        $mdln_row            = limpiar_datos($_POST['mdl_checkapuesta_n_row']);

        PartidoApuesta::delete($mdlidpartidoapuesta, $conexion);

        $focus_n_row = $mdln_row - 1;

        $success_display = 'show';
        $success_text = 'La apuesta ha sido eliminada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delpartidoapuesta
#region sub_apostar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostar'])) {
    try {
        $conexion->beginTransaction();

        $mdlidpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);
        $mdlriesgo_calculado = limpiar_datos($_POST['mdl_checkapuesta_riesgo_calculado']);
        $mdlriesgo           = limpiar_datos($_POST['mdl_checkapuesta_riesgo']);
        $mdlpotencial        = limpiar_datos($_POST['mdl_checkapuesta_potencial']);
        $mdlcupon            = limpiar_datos($_POST['mdl_checkapuesta_cupon']);
        $mdln_row               = limpiar_datos($_POST['mdl_checkapuesta_n_row']);

        $modpartidoapuesta = new PartidoApuesta();
        $modpartidoapuesta->id        = $mdlidpartidoapuesta;
        $modpartidoapuesta->riesgo    = $mdlriesgo;
        $modpartidoapuesta->potencial = $mdlpotencial;
        $modpartidoapuesta->id_cupon  = $mdlcupon;
        $modpartidoapuesta->modify_quick($conexion);
        $modpartidoapuesta->modify_marcar_apostado($conexion);

        $conexion->commit();

        $focus_n_row = $mdln_row - 1;

        $success_display = 'show';
        $success_text = 'La apuesta ha sido marcada como apostada.';

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostar
#region sub_verinfo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_verinfo'])) {
    try {
        $mdlidpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);

        $sel_partido_apuesta = PartidoApuesta::get($mdlidpartidoapuesta, $conexion);

        $_SESSION['idpartido'] = $sel_partido_apuesta->idpartido;

        header('Location: vpartidoinfo');
        exit();

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_verinfo
#region sub_verapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_verapuesta'])) {
    try {
        $mdlidpartidoapuesta = limpiar_datos($_POST['mdl_checkapuesta_idpartidoapuesta']);

        $sel_partido_apuesta = PartidoApuesta::get($mdlidpartidoapuesta, $conexion);

        $_SESSION['idpartido']     = $sel_partido_apuesta->idpartido;
        $_SESSION['idtipoapuesta'] = $sel_partido_apuesta->apuestatipo->id;
        $_SESSION['valorapuesta']  = $sel_partido_apuesta->valorapuesta;

        header('Location: ipartidootrasapuestas');
        exit();

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_verapuesta
#region sub_guardar_todo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_guardar_todo'])) {
    try {
        $guardar_solo = 1;

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_guardar_todo
#region try
try {
    $param = array();
    $param['orderby_winprobabilityporc'] = $orderby_winprobabilityporc;
    $apuestas = PartidoApuesta::get_list_solomarcadas($param, $conexion);

    $arriesgar = PartidoApuesta::sum_values($apuestas);

    $resultado = PartidoApuesta::calculate_percentage_vs_arriesgar($apuestas, $arriesgar, $riesgo_diario);
    $percentage_vs_arriesgado = $resultado['percentage_vs_arriesgar'];
    $sum_riesgo_ajustado      = $resultado['sum_riesgo_ajustado'];

    $preview_profit = PartidoApuesta::calculate_preview_profit($apuestas, $percentage_vs_arriesgado);

    PartidoApuesta::resaltar_repetidas($apuestas);

    //guardar las apuestas
    if($guardar_solo == 1){
        $param = array();
        $param['percentage_vs_arriesgado'] = $percentage_vs_arriesgado;
        $param['apuestas']                 = $apuestas;
        PartidoApuesta::modify_marcado_a_guardado_todos($param, $conexion);

        $apuestas = array();

        $success_display = 'show';
        $success_text = 'Las apuestas marcadas han sido guardadas.';
    }
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try
#region sub_del_all
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_del_all'])) {
    try {
        $conexion->beginTransaction();

        /** @var PartidoApuesta[] $apuestas */
        foreach ($apuestas as $apuesta) {
            PartidoApuesta::delete($apuesta->id, $conexion);
        }

        $conexion->commit();

        $apuestas = array();

        $success_display = 'show';
        $success_text = 'Las apuestas marcadas han sido eliminadas.';

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_del_all

require_once __ROOT__ . '/views/lpartidosapuestasmarcadas.view.php';

?>




