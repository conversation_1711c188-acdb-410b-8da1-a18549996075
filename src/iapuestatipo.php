<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/apuestatipo.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $method_sub_add = 1;
        
        $newapuestatipo = new ApuestaTipo();
        $newapuestatipo->nombre = limpiar_datos($_POST['nombre']);
        $newapuestatipo->val_penalidad = limpiar_datos($_POST['val_penalidad']);
        $newapuestatipo->perc_penalidad = limpiar_datos($_POST['perc_penalidad']);
        $newapuestatipo->min_valor_apuesta = limpiar_datos($_POST['min_valor_apuesta']);
        $newapuestatipo->max_valor_apuesta = limpiar_datos($_POST['max_valor_apuesta']);
        $newapuestatipo->def_valor_apuesta = limpiar_datos($_POST['def_valor_apuesta']);
        $newapuestatipo->n_puesto = limpiar_datos($_POST['n_puesto']);
        $newapuestatipo->add($conexion);

        header('Location: lapuestastipos?i=1');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add
#region try
try {
    $method_try = 1;

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/iapuestatipo.view.php';

?>